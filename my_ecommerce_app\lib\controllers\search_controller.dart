import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/product.dart';
import '../services/product_database_service.dart';

class SearchController extends GetxController {
  final ProductDatabaseService _productService = ProductDatabaseService();

  // حقل البحث
  final TextEditingController searchTextController = TextEditingController();

  // حالة البحث
  final RxBool isSearching = false.obs;
  final RxBool showFilters = false.obs;
  final RxString searchQuery = ''.obs;

  // نتائج البحث
  final RxList<Product> searchResults = <Product>[].obs;
  final RxList<Product> allProducts = <Product>[].obs;
  final RxList<String> searchHistory = <String>[].obs;
  final RxList<String> suggestions = <String>[].obs;

  // الفلاتر
  final RxString selectedCategory = ''.obs;
  final RxDouble minPrice = 0.0.obs;
  final RxDouble maxPrice = 10000.0.obs;
  final RxDouble minRating = 0.0.obs;
  final RxBool inStockOnly = false.obs;
  final RxBool freeShippingOnly = false.obs;
  final RxBool onSaleOnly = false.obs;

  // ترتيب النتائج
  final RxString sortBy =
      'relevance'.obs; // relevance, price_low, price_high, rating, newest

  // الفئات المتاحة
  final RxList<String> availableCategories = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();

    // مراقبة تغييرات النص
    searchTextController.addListener(() {
      searchQuery.value = searchTextController.text;
      if (searchTextController.text.isNotEmpty) {
        generateSuggestions(searchTextController.text);
      } else {
        suggestions.clear();
      }
    });
  }

  @override
  void onClose() {
    searchTextController.dispose();
    super.onClose();
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    try {
      // جلب جميع المنتجات
      final products = await _productService.getAllProducts();
      allProducts.assignAll(products);

      // جلب الفئات المتاحة
      final categories = await _productService.getAvailableCategories();
      availableCategories.assignAll(categories);

      // تحديد الحد الأقصى للسعر
      if (products.isNotEmpty) {
        final prices = products.map((p) => p.price).toList();
        maxPrice.value = prices.reduce((a, b) => a > b ? a : b);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات البحث: $e');
    }
  }

  /// البحث الرئيسي
  Future<void> search([String? query]) async {
    final searchText = query ?? searchTextController.text.trim();

    if (searchText.isEmpty && !hasActiveFilters) {
      searchResults.clear();
      return;
    }

    isSearching.value = true;

    try {
      List<Product> results = [];

      if (searchText.isNotEmpty) {
        // البحث في قاعدة البيانات
        results = await _productService.searchProducts(searchText);

        // إضافة للتاريخ
        addToSearchHistory(searchText);
      } else {
        // إذا لم يكن هناك نص بحث، استخدم جميع المنتجات
        results = List.from(allProducts);
      }

      // تطبيق الفلاتر
      results = applyFilters(results);

      // ترتيب النتائج
      results = sortResults(results, searchText);

      searchResults.assignAll(results);
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء البحث',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isSearching.value = false;
    }
  }

  /// تطبيق الفلاتر
  List<Product> applyFilters(List<Product> products) {
    return products.where((product) {
      // فلتر الفئة
      if (selectedCategory.value.isNotEmpty &&
          product.category != selectedCategory.value) {
        return false;
      }

      // فلتر السعر
      if (product.price < minPrice.value || product.price > maxPrice.value) {
        return false;
      }

      // فلتر التقييم
      if (product.rating < minRating.value) {
        return false;
      }

      // فلتر المخزون
      if (inStockOnly.value && product.stockQuantity <= 0) {
        return false;
      }

      // فلتر الشحن المجاني
      if (freeShippingOnly.value && !product.hasFreeShipping) {
        return false;
      }

      // فلتر العروض
      if (onSaleOnly.value && product.discountPercentage <= 0) {
        return false;
      }

      return true;
    }).toList();
  }

  /// ترتيب النتائج
  List<Product> sortResults(List<Product> products, String query) {
    switch (sortBy.value) {
      case 'price_low':
        products.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_high':
        products.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'rating':
        products.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'newest':
        products.sort((a, b) => b.id.compareTo(a.id));
        break;
      case 'relevance':
      default:
        // ترتيب حسب الصلة (البحث في الاسم أولاً، ثم الوصف)
        if (query.isNotEmpty) {
          products.sort((a, b) {
            final aNameMatch =
                a.name.toLowerCase().contains(query.toLowerCase());
            final bNameMatch =
                b.name.toLowerCase().contains(query.toLowerCase());

            if (aNameMatch && !bNameMatch) return -1;
            if (!aNameMatch && bNameMatch) return 1;

            // إذا كان كلاهما يطابق الاسم أو لا يطابق، رتب حسب التقييم
            return b.rating.compareTo(a.rating);
          });
        }
        break;
    }

    return products;
  }

  /// توليد اقتراحات البحث
  void generateSuggestions(String query) {
    if (query.length < 2) {
      suggestions.clear();
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    final productSuggestions = allProducts
        .where((product) =>
            product.name.toLowerCase().contains(lowercaseQuery) ||
            product.description.toLowerCase().contains(lowercaseQuery))
        .map((product) => product.name)
        .take(5)
        .toList();

    // إضافة اقتراحات من التاريخ
    final historySuggestions = searchHistory
        .where((item) => item.toLowerCase().contains(lowercaseQuery))
        .take(3)
        .toList();

    final allSuggestions = <String>{};
    allSuggestions.addAll(productSuggestions);
    allSuggestions.addAll(historySuggestions);

    suggestions.assignAll(allSuggestions.take(8).toList());
  }

  /// إضافة للتاريخ
  void addToSearchHistory(String query) {
    if (query.trim().isEmpty) return;

    searchHistory.remove(query);
    searchHistory.insert(0, query);

    // الاحتفاظ بآخر 10 عمليات بحث
    if (searchHistory.length > 10) {
      searchHistory.removeRange(10, searchHistory.length);
    }
  }

  /// مسح التاريخ
  void clearSearchHistory() {
    searchHistory.clear();
  }

  /// إعادة تعيين الفلاتر
  void resetFilters() {
    selectedCategory.value = '';
    minPrice.value = 0.0;
    maxPrice.value = allProducts.isNotEmpty
        ? allProducts.map((p) => p.price).reduce((a, b) => a > b ? a : b)
        : 10000.0;
    minRating.value = 0.0;
    inStockOnly.value = false;
    freeShippingOnly.value = false;
    onSaleOnly.value = false;
    sortBy.value = 'relevance';

    // إعادة البحث
    search();
  }

  /// تطبيق الفلاتر
  void applyFiltersAndSearch() {
    showFilters.value = false;
    search();
  }

  /// البحث بالاقتراح
  void searchWithSuggestion(String suggestion) {
    searchTextController.text = suggestion;
    searchQuery.value = suggestion;
    suggestions.clear();
    search(suggestion);
  }

  /// مسح البحث
  void clearSearch() {
    searchTextController.clear();
    searchQuery.value = '';
    searchResults.clear();
    suggestions.clear();
  }

  /// التحقق من وجود فلاتر نشطة
  bool get hasActiveFilters {
    return selectedCategory.value.isNotEmpty ||
        minPrice.value > 0 ||
        maxPrice.value <
            (allProducts.isNotEmpty
                ? allProducts
                    .map((p) => p.price)
                    .reduce((a, b) => a > b ? a : b)
                : 10000.0) ||
        minRating.value > 0 ||
        inStockOnly.value ||
        freeShippingOnly.value ||
        onSaleOnly.value;
  }

  /// عدد الفلاتر النشطة
  int get activeFiltersCount {
    int count = 0;
    if (selectedCategory.value.isNotEmpty) count++;
    if (minPrice.value > 0) count++;
    if (maxPrice.value <
        (allProducts.isNotEmpty
            ? allProducts.map((p) => p.price).reduce((a, b) => a > b ? a : b)
            : 10000.0)) {
      count++;
    }
    if (minRating.value > 0) count++;
    if (inStockOnly.value) count++;
    if (freeShippingOnly.value) count++;
    if (onSaleOnly.value) count++;
    return count;
  }

  /// الحصول على نص الترتيب
  String get sortText {
    switch (sortBy.value) {
      case 'price_low':
        return 'السعر: من الأقل للأعلى';
      case 'price_high':
        return 'السعر: من الأعلى للأقل';
      case 'rating':
        return 'التقييم';
      case 'newest':
        return 'الأحدث';
      case 'relevance':
      default:
        return 'الأكثر صلة';
    }
  }
}
