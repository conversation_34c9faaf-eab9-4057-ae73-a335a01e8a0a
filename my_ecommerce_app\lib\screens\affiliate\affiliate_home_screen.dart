import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/affiliate_controller.dart';
import '../../widgets/user_role_widgets.dart';
import '../../models/product.dart';
import '../../models/affiliate_link.dart';
import '../../models/commission.dart';
import '../../utils/mouse_event_handler.dart';

class AffiliateHomeScreen extends StatelessWidget {
  const AffiliateHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AffiliateController>();
    
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const UserRoleAppBar(title: "الصفحة الرئيسية - المسوق"),
      body: RefreshIndicator(
        onRefresh: () => controller.loadData(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 1. شريط الإحصائيات السريعة
              _buildQuickStatsBar(controller),
              
              const SizedBox(height: 20),
              
              // 2. الأدوات السريعة
              _buildQuickActionsSection(controller),
              
              const SizedBox(height: 20),
              
              // 3. المنتجات المقترحة للتسويق
              _buildRecommendedProducts(controller),
              
              const SizedBox(height: 20),
              
              // 4. أداء الروابط الحالية
              _buildLinksPerformance(controller),
              
              const SizedBox(height: 20),
              
              // 5. العمولات الأخيرة
              _buildRecentCommissions(controller),
              
              const SizedBox(height: 20),
              
              // 6. نصائح تسويقية
              _buildMarketingTips(),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildQuickCreateLinkFAB(controller),
    );
  }

  // شريط الإحصائيات السريعة
  Widget _buildQuickStatsBar(AffiliateController controller) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade400, Colors.orange.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '📊 إحصائيات اليوم',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Obx(() => Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'نقرات اليوم',
                  '${controller.todayStats['todayClicks'] ?? 0}',
                  Icons.mouse,
                  Colors.white,
                ),
              ),
              Expanded(
                child: _buildStatCard(
                  'عمولات اليوم',
                  '${(controller.todayStats['todayEarnings'] ?? 0.0).toStringAsFixed(2)} دج',
                  Icons.monetization_on,
                  Colors.white,
                ),
              ),
              Expanded(
                child: _buildStatCard(
                  'مبيعات اليوم',
                  '${controller.todayStats['todaySales'] ?? 0}',
                  Icons.shopping_cart,
                  Colors.white,
                ),
              ),
              Expanded(
                child: _buildStatCard(
                  'معدل التحويل',
                  '${(controller.todayStats['todayConversionRate'] ?? 0.0).toStringAsFixed(1)}%',
                  Icons.trending_up,
                  Colors.white,
                ),
              ),
            ],
          )),
        ],
      ),
    );
  }

  // بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: color.withOpacity(0.9),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // قسم الأدوات السريعة
  Widget _buildQuickActionsSection(AffiliateController controller) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.flash_on, color: Colors.orange, size: 24),
                SizedBox(width: 8),
                Text(
                  'الأدوات السريعة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'إنشاء رابط',
                    Icons.add_link,
                    Colors.blue,
                    () => _showCreateLinkDialog(controller),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    'مشاركة منتج',
                    Icons.share,
                    Colors.green,
                    () => _showShareProductDialog(controller),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    'العمولات',
                    Icons.account_balance_wallet,
                    Colors.orange,
                    () => Get.toNamed('/affiliate-dashboard'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    'تقرير سريع',
                    Icons.analytics,
                    Colors.purple,
                    () => _generateQuickReport(controller),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // زر أداة سريعة
  Widget _buildQuickActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SafeGestureDetector(
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color.withOpacity(0.1),
          foregroundColor: color,
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: color.withOpacity(0.3)),
          ),
        ),
        child: Column(
          children: [
            Icon(icon, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // المنتجات المقترحة للتسويق
  Widget _buildRecommendedProducts(AffiliateController controller) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.star, color: Colors.amber, size: 24),
                    SizedBox(width: 8),
                    Text(
                      'منتجات مقترحة للتسويق',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () => Get.toNamed('/affiliate-dashboard'),
                  icon: const Icon(Icons.arrow_forward, size: 16),
                  label: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // فلاتر سريعة
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('عمولة عالية', true, Colors.green),
                  _buildFilterChip('جديد', false, Colors.blue),
                  _buildFilterChip('رائج', false, Colors.orange),
                  _buildFilterChip('عرض خاص', false, Colors.red),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // قائمة المنتجات
            Obx(() => SizedBox(
              height: 280,
              child: controller.recommendedProducts.isEmpty
                ? const Center(
                    child: Text(
                      'لا توجد منتجات مقترحة حالياً',
                      style: TextStyle(color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.recommendedProducts.length,
                    itemBuilder: (context, index) {
                      final product = controller.recommendedProducts[index];
                      return Container(
                        width: 200,
                        margin: const EdgeInsets.only(left: 12),
                        child: _buildAffiliateProductCard(product, controller),
                      );
                    },
                  ),
            )),
          ],
        ),
      ),
    );
  }

  // فلتر شيب
  Widget _buildFilterChip(String label, bool isSelected, Color color) {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          // تطبيق الفلتر
        },
        selectedColor: color.withOpacity(0.2),
        checkmarkColor: color,
        labelStyle: TextStyle(
          color: isSelected ? color : Colors.grey[600],
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  // بطاقة منتج للمسوق
  Widget _buildAffiliateProductCard(Product product, AffiliateController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المنتج
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Container(
              height: 120,
              width: double.infinity,
              color: Colors.grey[200],
              child: product.imageUrls.isNotEmpty
                ? Image.network(
                    product.imageUrls.first,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.image, size: 40, color: Colors.grey);
                    },
                  )
                : const Icon(Icons.image, size: 40, color: Colors.grey),
            ),
          ),
          
          // معلومات المنتج
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${product.price.toStringAsFixed(2)} دج',
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'عمولة: ${(product.price * 0.1).toStringAsFixed(2)} دج',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _createProductLink(product, controller),
                          icon: const Icon(Icons.link, size: 16),
                          label: const Text('رابط', style: TextStyle(fontSize: 12)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            minimumSize: Size.zero,
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      IconButton(
                        onPressed: () => _shareProduct(product),
                        icon: const Icon(Icons.share, size: 16),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.green.withOpacity(0.1),
                          foregroundColor: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // أداء الروابط الحالية
  Widget _buildLinksPerformance(AffiliateController controller) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.trending_up, color: Colors.blue, size: 24),
                    SizedBox(width: 8),
                    Text(
                      'أفضل الروابط أداءً',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () => Get.toNamed('/affiliate-dashboard'),
                  icon: const Icon(Icons.arrow_forward, size: 16),
                  label: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() => controller.topPerformingLinks.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: Text(
                      'لا توجد روابط حالياً',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
                )
              : Column(
                  children: controller.topPerformingLinks.take(3).map((link) {
                    return _buildLinkPerformanceCard(link);
                  }).toList(),
                ),
            ),
          ],
        ),
      ),
    );
  }

  // بطاقة أداء الرابط
  Widget _buildLinkPerformanceCard(AffiliateLink link) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  link.productId ?? 'منتج غير محدد',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getLinkStatusColor(link.isActive),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  link.isActive ? 'نشط' : 'غير نشط',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildLinkStat('النقرات', '${link.clickCount}', Colors.blue),
              _buildLinkStat('التحويلات', '${link.conversionCount}', Colors.green),
              _buildLinkStat('الأرباح', '${link.totalEarnings.toStringAsFixed(2)} دج', Colors.orange),
              _buildLinkStat('معدل التحويل', '${link.conversionRate.toStringAsFixed(1)}%', Colors.purple),
            ],
          ),
        ],
      ),
    );
  }

  // إحصائية الرابط
  Widget _buildLinkStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 14,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 11, color: Colors.grey),
        ),
      ],
    );
  }

  // العمولات الأخيرة
  Widget _buildRecentCommissions(AffiliateController controller) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.monetization_on, color: Colors.green, size: 24),
                    SizedBox(width: 8),
                    Text(
                      'العمولات الأخيرة',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () => Get.toNamed('/affiliate-dashboard'),
                  icon: const Icon(Icons.arrow_forward, size: 16),
                  label: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() => controller.recentCommissions.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: Text(
                      'لا توجد عمولات حالياً',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
                )
              : Column(
                  children: controller.recentCommissions.take(5).map((commission) {
                    return _buildCommissionCard(commission);
                  }).toList(),
                ),
            ),
          ],
        ),
      ),
    );
  }

  // بطاقة العمولة
  Widget _buildCommissionCard(Commission commission) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 40,
            decoration: BoxDecoration(
              color: _getCommissionStatusColor(commission.status),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  commission.productId,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  _formatDate(commission.createdAt),
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${commission.commissionAmount.toStringAsFixed(2)} دج',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                  fontSize: 14,
                ),
              ),
              Text(
                _getCommissionStatusText(commission.status),
                style: TextStyle(
                  color: _getCommissionStatusColor(commission.status),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // نصائح تسويقية
  Widget _buildMarketingTips() {
    final tips = [
      {
        'icon': Icons.lightbulb,
        'title': 'استخدم وسائل التواصل الاجتماعي',
        'description': 'شارك منتجاتك على فيسبوك وإنستغرام لزيادة المبيعات',
        'color': Colors.blue,
      },
      {
        'icon': Icons.schedule,
        'title': 'اختر الوقت المناسب للنشر',
        'description': 'انشر في الأوقات التي يكون فيها جمهورك أكثر نشاطاً',
        'color': Colors.orange,
      },
      {
        'icon': Icons.star,
        'title': 'ركز على المنتجات عالية التقييم',
        'description': 'المنتجات ذات التقييم العالي تحقق مبيعات أكثر',
        'color': Colors.amber,
      },
    ];

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.tips_and_updates, color: Colors.purple, size: 24),
                SizedBox(width: 8),
                Text(
                  'نصائح تسويقية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...tips.map((tip) => _buildTipCard(tip)).toList(),
          ],
        ),
      ),
    );
  }

  // بطاقة نصيحة
  Widget _buildTipCard(Map<String, dynamic> tip) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: (tip['color'] as Color).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: (tip['color'] as Color).withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            tip['icon'] as IconData,
            color: tip['color'] as Color,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tip['title'] as String,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  tip['description'] as String,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // زر الإنشاء السريع
  Widget _buildQuickCreateLinkFAB(AffiliateController controller) {
    return FloatingActionButton.extended(
      onPressed: () => _showCreateLinkDialog(controller),
      backgroundColor: Colors.orange,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add_link),
      label: const Text('رابط سريع'),
    );
  }

  // دوال مساعدة
  Color _getLinkStatusColor(bool isActive) {
    return isActive ? Colors.green : Colors.grey;
  }

  Color _getCommissionStatusColor(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return Colors.orange;
      case CommissionStatus.approved:
        return Colors.blue;
      case CommissionStatus.processing:
        return Colors.purple;
      case CommissionStatus.earned:
        return Colors.green;
      case CommissionStatus.paid:
        return Colors.teal;
      case CommissionStatus.cancelled:
        return Colors.red;
      case CommissionStatus.refunded:
        return Colors.grey;
    }
  }

  String _getCommissionStatusText(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return 'معلقة';
      case CommissionStatus.approved:
        return 'معتمدة';
      case CommissionStatus.processing:
        return 'قيد المعالجة';
      case CommissionStatus.earned:
        return 'مستحقة';
      case CommissionStatus.paid:
        return 'مدفوعة';
      case CommissionStatus.cancelled:
        return 'ملغية';
      case CommissionStatus.refunded:
        return 'مسترجعة';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // دوال الأحداث
  void _showCreateLinkDialog(AffiliateController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء رابط سريع'),
        content: const Text('اختر منتج لإنشاء رابط تسويقي له'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.toNamed('/affiliate-dashboard');
            },
            child: const Text('اختيار منتج'),
          ),
        ],
      ),
    );
  }

  void _showShareProductDialog(AffiliateController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('مشاركة منتج'),
        content: const Text('اختر منتج لمشاركته على وسائل التواصل'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.toNamed('/affiliate-dashboard');
            },
            child: const Text('اختيار منتج'),
          ),
        ],
      ),
    );
  }

  void _generateQuickReport(AffiliateController controller) {
    Get.snackbar(
      'تقرير سريع',
      'جاري إنشاء التقرير...',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.blue.withOpacity(0.8),
      colorText: Colors.white,
    );

    // محاكاة إنشاء التقرير
    Future.delayed(const Duration(seconds: 2), () {
      Get.snackbar(
        'تم إنشاء التقرير',
        'تم إنشاء تقرير الأداء بنجاح',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    });
  }

  void _createProductLink(Product product, AffiliateController controller) {
    // تعيين نوع الرابط والمنتج
    controller.selectedLinkType.value = LinkType.product;
    controller.productUrlController.text = '/product/${product.id}';

    // إنشاء الرابط
    controller.createAffiliateLink();

    Get.snackbar(
      'تم إنشاء الرابط',
      'تم إنشاء رابط تسويقي لـ ${product.name}',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.blue.withValues(alpha: 0.8),
      colorText: Colors.white,
    );
  }

  void _shareProduct(Product product) {
    Get.snackbar(
      'مشاركة المنتج',
      'تم نسخ رابط ${product.name} للمشاركة',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green.withValues(alpha: 0.8),
      colorText: Colors.white,
    );
  }
}
