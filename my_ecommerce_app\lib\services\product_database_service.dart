import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/product.dart';

/// خدمة قاعدة البيانات للمنتجات
class ProductDatabaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== إدارة المنتجات ====================

  /// جلب جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Product.fromJson(doc.data())).toList();
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// جلب منتج بالمعرف
  Future<Product?> getProductById(String productId) async {
    try {
      final doc = await _firestore.collection('products').doc(productId).get();

      if (doc.exists) {
        return Product.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في جلب المنتج: $e');
      return null;
    }
  }

  /// إضافة منتج جديد
  Future<bool> addProduct(Product product) async {
    try {
      await _firestore
          .collection('products')
          .doc(product.id.toString())
          .set(product.toJson());
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تحديث منتج
  Future<bool> updateProduct(
      String productId, Map<String, dynamic> updates) async {
    try {
      await _firestore.collection('products').doc(productId).update(updates);
      return true;
    } catch (e) {
      print('خطأ في تحديث المنتج: $e');
      return false;
    }
  }

  /// حذف منتج
  Future<bool> deleteProduct(String productId) async {
    try {
      await _firestore.collection('products').doc(productId).delete();
      return true;
    } catch (e) {
      print('خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// جلب المنتجات بالفئة
  Future<List<Product>> getProductsByCategory(String category) async {
    try {
      // جلب المنتجات بالفئة بدون ترتيب لتجنب مشكلة الفهرس
      final snapshot = await _firestore
          .collection('products')
          .where('category', isEqualTo: category)
          .limit(50) // تحديد عدد المنتجات
          .get();

      // تحويل البيانات وترتيبها محلياً
      List<Product> products =
          snapshot.docs.map((doc) => Product.fromJson(doc.data())).toList();

      // ترتيب المنتجات محلياً حسب تاريخ الإنشاء
      products.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return products;
    } catch (e) {
      print('خطأ في جلب المنتجات بالفئة: $e');
      return [];
    }
  }

  /// جلب المنتجات المميزة
  Future<List<Product>> getFeaturedProducts() async {
    try {
      print('🔄 بدء جلب المنتجات المميزة...');

      // محاولة جلب المنتجات المميزة أولاً
      var snapshot = await _firestore
          .collection('products')
          .where('isFeatured', isEqualTo: true)
          .limit(20)
          .get();

      List<Product> products = [];

      // إذا لم توجد منتجات مميزة، جلب أحدث المنتجات
      if (snapshot.docs.isEmpty) {
        print('⚠️ لا توجد منتجات مميزة، جلب أحدث المنتجات...');
        snapshot = await _firestore.collection('products').limit(20).get();
      }

      // تحويل البيانات
      products = snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // إضافة المعرف
        return Product.fromJson(data);
      }).toList();

      // ترتيب المنتجات محلياً حسب تاريخ الإنشاء
      products.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      print('✅ تم جلب ${products.length} منتج مميز');
      return products;
    } catch (e) {
      print('❌ خطأ في جلب المنتجات المميزة: $e');
      // إرجاع منتجات تجريبية في حالة الخطأ
      return _createSampleProducts();
    }
  }

  /// إنشاء منتجات تجريبية للاختبار
  List<Product> _createSampleProducts() {
    return [
      Product(
        id: 1,
        name: 'نظارة شمسية عصرية',
        description:
            'نظارة شمسية أنيقة مع حماية UV كاملة، مثالية للاستخدام اليومي',
        price: 2500.0,
        originalPrice: 3000.0,
        imageUrls: ['placeholder_sunglasses'],
        category: 'sunglasses',
        brand: 'Ray-Ban',
        stockQuantity: 10,
        rating: 4.5,
        reviewCount: 25,
        type: EyewearType.sunglasses,
        targetGender: Gender.unisex,
        specs: EyewearSpecs(
          frameColor: 'أسود',
          lensColor: 'رمادي',
          frameMaterial: FrameMaterial.plastic,
          lensType: LensType.polarized,
          frameSize: '55-18-145',
          lensWidth: 55,
          bridgeWidth: 18,
          templeLength: 145,
          hasUVProtection: true,
        ),
        createdAt: DateTime.now(),
        isFeatured: true,
        hasFreeShipping: true,
        discountEndDate: DateTime.now().add(const Duration(days: 7)),
      ),
      Product(
        id: 2,
        name: 'نظارة قراءة مريحة',
        description:
            'نظارة قراءة مريحة للاستخدام اليومي مع عدسات مضادة للضوء الأزرق',
        price: 1800.0,
        originalPrice: 2200.0,
        imageUrls: ['placeholder_reading'],
        category: 'reading',
        brand: 'Oakley',
        stockQuantity: 15,
        rating: 4.2,
        reviewCount: 18,
        type: EyewearType.reading,
        targetGender: Gender.unisex,
        specs: EyewearSpecs(
          frameColor: 'بني',
          lensColor: 'شفاف',
          frameMaterial: FrameMaterial.metal,
          lensType: LensType.blueLight,
          frameSize: '52-16-140',
          lensWidth: 52,
          bridgeWidth: 16,
          templeLength: 140,
          hasUVProtection: false,
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isFeatured: true,
        hasFreeShipping: false,
        discountEndDate: DateTime.now().add(const Duration(hours: 12)),
      ),
      Product(
        id: 3,
        name: 'نظارة رياضية احترافية',
        description: 'نظارة رياضية مقاومة للصدمات مع تقنية مضادة للانزلاق',
        price: 3200.0,
        originalPrice: 3800.0,
        imageUrls: ['placeholder_sports'],
        category: 'sports',
        brand: 'Nike',
        stockQuantity: 8,
        rating: 4.7,
        reviewCount: 32,
        type: EyewearType.sports,
        targetGender: Gender.unisex,
        specs: EyewearSpecs(
          frameColor: 'أحمر',
          lensColor: 'أسود',
          frameMaterial: FrameMaterial.plastic,
          lensType: LensType.polarized,
          frameSize: '58-20-150',
          lensWidth: 58,
          bridgeWidth: 20,
          templeLength: 150,
          hasUVProtection: true,
        ),
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        isFeatured: true,
        hasFreeShipping: true,
        discountEndDate: DateTime.now().add(const Duration(days: 3)),
      ),
    ];
  }

  /// جلب المنتجات المتاحة
  Future<List<Product>> getAvailableProducts() async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .where('stockQuantity', isGreaterThan: 0)
          .orderBy('stockQuantity', descending: true)
          .get();

      return snapshot.docs.map((doc) => Product.fromJson(doc.data())).toList();
    } catch (e) {
      print('خطأ في جلب المنتجات المتاحة: $e');
      return [];
    }
  }

  /// جلب المنتجات منخفضة المخزون
  Future<List<Product>> getLowStockProducts({int threshold = 5}) async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .where('stockQuantity', isLessThanOrEqualTo: threshold)
          .where('stockQuantity', isGreaterThan: 0)
          .orderBy('stockQuantity', descending: false)
          .get();

      return snapshot.docs.map((doc) => Product.fromJson(doc.data())).toList();
    } catch (e) {
      print('خطأ في جلب المنتجات منخفضة المخزون: $e');
      return [];
    }
  }

  /// جلب المنتجات غير المتاحة
  Future<List<Product>> getOutOfStockProducts() async {
    try {
      // جلب المنتجات غير المتاحة بدون ترتيب لتجنب مشكلة الفهرس
      final snapshot = await _firestore
          .collection('products')
          .where('stockQuantity', isEqualTo: 0)
          .limit(50) // تحديد عدد المنتجات
          .get();

      // تحويل البيانات وترتيبها محلياً
      List<Product> products =
          snapshot.docs.map((doc) => Product.fromJson(doc.data())).toList();

      // ترتيب المنتجات محلياً حسب تاريخ الإنشاء
      products.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return products;
    } catch (e) {
      print('خطأ في جلب المنتجات غير المتاحة: $e');
      return [];
    }
  }

  /// تحديث مخزون المنتج
  Future<bool> updateProductStock(String productId, int newStock) async {
    try {
      await _firestore.collection('products').doc(productId).update({
        'stockQuantity': newStock,
        'updatedAt': DateTime.now().toIso8601String(),
      });
      return true;
    } catch (e) {
      print('خطأ في تحديث مخزون المنتج: $e');
      return false;
    }
  }

  /// تقليل مخزون المنتج (عند الشراء)
  Future<bool> decreaseProductStock(String productId, int quantity) async {
    try {
      final doc = await _firestore.collection('products').doc(productId).get();

      if (!doc.exists) {
        return false;
      }

      final currentStock = doc.data()!['stockQuantity'] as int;
      if (currentStock < quantity) {
        return false; // مخزون غير كافي
      }

      await _firestore.collection('products').doc(productId).update({
        'stockQuantity': currentStock - quantity,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      print('خطأ في تقليل مخزون المنتج: $e');
      return false;
    }
  }

  /// زيادة مخزون المنتج (عند الإلغاء أو الإرجاع)
  Future<bool> increaseProductStock(String productId, int quantity) async {
    try {
      final doc = await _firestore.collection('products').doc(productId).get();

      if (!doc.exists) {
        return false;
      }

      final currentStock = doc.data()!['stockQuantity'] as int;

      await _firestore.collection('products').doc(productId).update({
        'stockQuantity': currentStock + quantity,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      print('خطأ في زيادة مخزون المنتج: $e');
      return false;
    }
  }

  /// جلب إحصائيات المنتجات
  Future<Map<String, dynamic>> getProductStats() async {
    try {
      final allProductsSnapshot = await _firestore.collection('products').get();
      final availableProductsSnapshot = await _firestore
          .collection('products')
          .where('stockQuantity', isGreaterThan: 0)
          .get();
      final outOfStockSnapshot = await _firestore
          .collection('products')
          .where('stockQuantity', isEqualTo: 0)
          .get();
      final lowStockSnapshot = await _firestore
          .collection('products')
          .where('stockQuantity', isLessThanOrEqualTo: 5)
          .where('stockQuantity', isGreaterThan: 0)
          .get();

      double totalValue = 0;
      int totalStock = 0;

      for (final doc in allProductsSnapshot.docs) {
        final data = doc.data();
        final price = (data['price'] as num?)?.toDouble() ?? 0;
        final stock = (data['stockQuantity'] as num?)?.toInt() ?? 0;
        totalValue += price * stock;
        totalStock += stock;
      }

      return {
        'totalProducts': allProductsSnapshot.docs.length,
        'availableProducts': availableProductsSnapshot.docs.length,
        'outOfStockProducts': outOfStockSnapshot.docs.length,
        'lowStockProducts': lowStockSnapshot.docs.length,
        'totalInventoryValue': totalValue,
        'totalStock': totalStock,
      };
    } catch (e) {
      print('خطأ في جلب إحصائيات المنتجات: $e');
      return {};
    }
  }

  /// جلب الفئات المتاحة
  Future<List<String>> getAvailableCategories() async {
    try {
      final snapshot = await _firestore.collection('products').get();
      final categories = <String>{};

      for (final doc in snapshot.docs) {
        final category = doc.data()['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }

      return categories.toList()..sort();
    } catch (e) {
      print('خطأ في جلب الفئات: $e');
      return [];
    }
  }

  /// إضافة البيانات التجريبية إلى قاعدة البيانات
  Future<bool> addDemoProducts() async {
    try {
      final demoProducts = Product.demoProducts;

      for (final product in demoProducts) {
        await _firestore
            .collection('products')
            .doc(product.id.toString())
            .set(product.toJson());
      }

      print('تم إضافة ${demoProducts.length} منتج تجريبي بنجاح');
      return true;
    } catch (e) {
      print('خطأ في إضافة البيانات التجريبية: $e');
      return false;
    }
  }

  /// التحقق من وجود منتجات في قاعدة البيانات
  Future<bool> hasProducts() async {
    try {
      final snapshot = await _firestore.collection('products').limit(1).get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      print('خطأ في التحقق من وجود المنتجات: $e');
      return false;
    }
  }

  /// مسح جميع المنتجات (للاختبار فقط)
  Future<bool> clearAllProducts() async {
    try {
      final snapshot = await _firestore.collection('products').get();

      for (final doc in snapshot.docs) {
        await doc.reference.delete();
      }

      print('تم مسح جميع المنتجات');
      return true;
    } catch (e) {
      print('خطأ في مسح المنتجات: $e');
      return false;
    }
  }

  /// البحث في المنتجات
  Future<List<Product>> searchProducts(String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getAllProducts();
      }

      final lowercaseQuery = query.toLowerCase();

      // جلب جميع المنتجات أولاً (يمكن تحسينها لاحقاً بالبحث في قاعدة البيانات)
      final allProducts = await getAllProducts();

      // فلترة المنتجات محلياً
      final filteredProducts = allProducts.where((product) {
        return product.name.toLowerCase().contains(lowercaseQuery) ||
            product.description.toLowerCase().contains(lowercaseQuery) ||
            product.brand.toLowerCase().contains(lowercaseQuery) ||
            product.category.toLowerCase().contains(lowercaseQuery) ||
            product.tags
                .any((tag) => tag.toLowerCase().contains(lowercaseQuery));
      }).toList();

      return filteredProducts;
    } catch (e) {
      print('خطأ في البحث: $e');
      return [];
    }
  }

  /// البحث المتقدم مع فلاتر
  Future<List<Product>> advancedSearch({
    String? query,
    String? category,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    bool? inStockOnly,
    bool? freeShippingOnly,
    bool? onSaleOnly,
  }) async {
    try {
      // البحث الأساسي
      List<Product> results = query != null && query.isNotEmpty
          ? await searchProducts(query)
          : await getAllProducts();

      // تطبيق الفلاتر
      results = results.where((product) {
        // فلتر الفئة
        if (category != null &&
            category.isNotEmpty &&
            product.category != category) {
          return false;
        }

        // فلتر السعر
        if (minPrice != null && product.price < minPrice) {
          return false;
        }
        if (maxPrice != null && product.price > maxPrice) {
          return false;
        }

        // فلتر التقييم
        if (minRating != null && product.rating < minRating) {
          return false;
        }

        // فلتر المخزون
        if (inStockOnly == true && product.stockQuantity <= 0) {
          return false;
        }

        // فلتر الشحن المجاني
        if (freeShippingOnly == true && !product.hasFreeShipping) {
          return false;
        }

        // فلتر العروض
        if (onSaleOnly == true && product.discountPercentage <= 0) {
          return false;
        }

        return true;
      }).toList();

      return results;
    } catch (e) {
      print('خطأ في البحث المتقدم: $e');
      return [];
    }
  }
}
