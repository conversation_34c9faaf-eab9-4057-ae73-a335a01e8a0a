import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// أدوات تحسين الأداء
class PerformanceUtils {
  
  /// تشغيل مهمة بعد انتهاء الإطار الحالي
  static void runAfterFrame(VoidCallback callback) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      callback();
    });
  }

  /// تشغيل مهمة في الإطار التالي
  static void runInNextFrame(VoidCallback callback) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      callback();
    });
  }

  /// تأخير تنفيذ مهمة لتجنب blocking UI
  static Future<void> runWithDelay(VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 1),
  }) async {
    await Future.delayed(delay);
    callback();
  }

  /// تشغيل مهمة في microtask
  static void runInMicrotask(VoidCallback callback) {
    Future.microtask(callback);
  }

  /// wrapper آمن للـ setState
  static void safeSetState(State state, VoidCallback callback) {
    if (state.mounted) {
      runAfterFrame(() {
        if (state.mounted) {
          callback();
        }
      });
    }
  }

  /// تحسين أداء القوائم الطويلة
  static Widget optimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
  }) {
    return ListView.builder(
      controller: controller,
      shrinkWrap: shrinkWrap,
      physics: physics,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      // تحسينات إضافية للأداء
      cacheExtent: 500.0,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: true,
    );
  }

  /// wrapper محسن للصور
  static Widget optimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit? fit,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return RepaintBoundary(
      child: Image.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return placeholder ?? const CircularProgressIndicator();
        },
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ?? const Icon(Icons.error);
        },
        // تحسينات الذاكرة
        cacheWidth: width?.toInt(),
        cacheHeight: height?.toInt(),
      ),
    );
  }

  /// قياس أداء العمليات
  static Future<T> measurePerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    if (!kDebugMode) {
      return await operation();
    }

    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      debugPrint('⏱️ $operationName took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ $operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// تنظيف الذاكرة
  static void cleanupMemory() {
    if (kDebugMode) {
      debugPrint('🧹 تنظيف الذاكرة...');
    }
    
    // تنظيف cache الصور
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    
    // إجبار garbage collection
    runInMicrotask(() {
      // هذا سيساعد في تنظيف الذاكرة
    });
  }

  /// مراقبة استخدام الذاكرة
  static void monitorMemoryUsage() {
    if (!kDebugMode) return;

    runInNextFrame(() {
      final imageCache = PaintingBinding.instance.imageCache;
      debugPrint('📊 Image Cache: ${imageCache.currentSize} images, ${imageCache.currentSizeBytes} bytes');
    });
  }

  /// تحسين الرسوم المتحركة
  static AnimationController createOptimizedAnimationController({
    required TickerProvider vsync,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }

  /// wrapper محسن للـ FutureBuilder
  static Widget optimizedFutureBuilder<T>({
    required Future<T> future,
    required Widget Function(BuildContext, AsyncSnapshot<T>) builder,
    T? initialData,
  }) {
    return RepaintBoundary(
      child: FutureBuilder<T>(
        future: future,
        initialData: initialData,
        builder: builder,
      ),
    );
  }

  /// wrapper محسن للـ StreamBuilder
  static Widget optimizedStreamBuilder<T>({
    required Stream<T> stream,
    required Widget Function(BuildContext, AsyncSnapshot<T>) builder,
    T? initialData,
  }) {
    return RepaintBoundary(
      child: StreamBuilder<T>(
        stream: stream,
        initialData: initialData,
        builder: builder,
      ),
    );
  }
}
