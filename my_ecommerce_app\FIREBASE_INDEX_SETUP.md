# Firebase Firestore Index Setup Guide

## Required Composite Indexes

To fix the Firebase index errors, you need to create the following composite indexes in your Firebase Console:

### 1. Products Collection - Featured Products Index

**Collection**: `products`
**Fields**:
- `isFeatured` (Ascending)
- `createdAt` (Descending)
- `__name__` (Ascending)

**How to create**:
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `my-ecommerce-app-61373`
3. Navigate to Firestore Database > Indexes
4. Click "Create Index"
5. Set Collection ID: `products`
6. Add fields as specified above
7. Click "Create"

### 2. Alternative Solution (Currently Implemented)

The code has been temporarily modified to avoid the composite index requirement by:
- Removing the `orderBy` clause from the Firestore query
- Sorting the results locally in the app
- Adding a `limit(20)` to improve performance

### 3. For Production Use

For better performance in production, it's recommended to:
1. Create the composite index as described above
2. Revert to the original query with `orderBy`

**Original query (requires index)**:
```dart
final snapshot = await _firestore
    .collection('products')
    .where('isFeatured', isEqualTo: true)
    .orderBy('createdAt', descending: true)
    .get();
```

**Current query (no index required)**:
```dart
final snapshot = await _firestore
    .collection('products')
    .where('isFeatured', isEqualTo: true)
    .limit(20)
    .get();
```

## Index Creation URL

You can also create the index directly using this URL:
https://console.firebase.google.com/v1/r/project/my-ecommerce-app-61373/firestore/indexes?create_composite=Cldwcm9qZWN0cy9teS1lY29tbWVyY2UtYXBwLTYxMzczL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9wcm9kdWN0cy9pbmRleGVzL18QARoOCgppc0ZlYXR1cmVkEAEaDQoJY3JlYXRlZEF0EAIaDAoIX19uYW1lX18QAg

## Notes

- Index creation can take several minutes to complete
- The app will work with the current implementation while the index is being built
- Monitor the Firebase Console for index build status