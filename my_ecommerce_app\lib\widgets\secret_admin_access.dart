import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// ويدجت مخفي للوصول لتسجيل دخول الإدمن
class SecretAdminAccess extends StatefulWidget {
  final Widget child;
  
  const SecretAdminAccess({
    super.key,
    required this.child,
  });

  @override
  State<SecretAdminAccess> createState() => _SecretAdminAccessState();
}

class _SecretAdminAccessState extends State<SecretAdminAccess> {
  int _tapCount = 0;
  DateTime? _lastTapTime;
  
  // تسلسل النقرات المطلوب للوصول لتسجيل دخول الإدمن
  static const int _requiredTaps = 7;
  static const Duration _tapTimeout = Duration(seconds: 3);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: widget.child,
    );
  }

  void _handleTap() {
    final now = DateTime.now();
    
    // إعادة تعيين العداد إذا انتهت المهلة الزمنية
    if (_lastTapTime != null && 
        now.difference(_lastTapTime!) > _tapTimeout) {
      _tapCount = 0;
    }
    
    _tapCount++;
    _lastTapTime = now;
    
    // إظهار تلميح للمطورين
    if (_tapCount == 3) {
      _showHint('استمر في النقر... (${_requiredTaps - _tapCount} متبقية)');
    } else if (_tapCount == 5) {
      _showHint('تقريباً... (${_requiredTaps - _tapCount} متبقية)');
    }
    
    // الوصول لتسجيل دخول الإدمن
    if (_tapCount >= _requiredTaps) {
      _tapCount = 0;
      _showAdminAccessDialog();
    }
  }

  void _showHint(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 1),
          backgroundColor: Colors.grey[800],
        ),
      );
    }
  }

  void _showAdminAccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.admin_panel_settings, color: Colors.red[800]),
            const SizedBox(width: 8),
            const Text('وصول الإدارة'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم اكتشاف محاولة الوصول لوحة الإدارة.'),
            SizedBox(height: 8),
            Text(
              'هذه المنطقة مخصصة للمديرين فقط.',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.toNamed('/secret-admin-login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[800],
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل دخول الإدارة'),
          ),
        ],
      ),
    );
  }
}

/// ويدجت بديل أبسط - زر مخفي في الزاوية
class HiddenAdminButton extends StatelessWidget {
  const HiddenAdminButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onLongPress: () {
          _showAdminAccessDialog(context);
        },
        child: Container(
          width: 50,
          height: 50,
          color: Colors.transparent,
          child: const Center(
            child: Icon(
              Icons.admin_panel_settings,
              color: Colors.transparent,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  void _showAdminAccessDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.security, color: Colors.red[800]),
            const SizedBox(width: 8),
            const Text('منطقة آمنة'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.admin_panel_settings, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'وصول الإدارة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'هذه منطقة مقيدة للمديرين فقط.\nيرجى إدخال بيانات الإدمن للمتابعة.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.toNamed('/secret-admin-login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[800],
              foregroundColor: Colors.white,
            ),
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }
}

/// طريقة أخرى - كود سري في شريط البحث
class SecretAdminCode {
  static const String _secretCode = 'admin2024';
  
  static bool checkSecretCode(String input) {
    return input.toLowerCase().trim() == _secretCode;
  }
  
  static void handleSecretCode(String input) {
    if (checkSecretCode(input)) {
      Get.snackbar(
        'تم اكتشاف الكود السري',
        'سيتم توجيهك لتسجيل دخول الإدارة',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red[800],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
      
      // تأخير قصير ثم التوجه لصفحة الإدمن
      Future.delayed(const Duration(seconds: 1), () {
        Get.toNamed('/secret-admin-login');
      });
    }
  }
}
