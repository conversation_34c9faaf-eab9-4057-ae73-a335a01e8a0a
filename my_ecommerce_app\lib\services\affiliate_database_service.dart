import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/commission.dart';
import '../models/affiliate_link.dart';
import '../models/affiliate_request.dart';
import '../models/user.dart';

/// خدمة قاعدة البيانات الخاصة بالمسوقين
class AffiliateDatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== إدارة الروابط التسويقية ====================

  /// جلب روابط مسوق معين
  Future<List<AffiliateLink>> getAffiliateLinks(String affiliateId) async {
    try {
      final snapshot = await _firestore
          .collection('affiliate_links')
          .where('affiliateId', isEqualTo: affiliateId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => AffiliateLink.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب الروابط التسويقية: $e');
      return [];
    }
  }

  /// إنشاء رابط تسويقي جديد
  Future<bool> createAffiliateLink(AffiliateLink link) async {
    try {
      await _firestore.collection('affiliate_links').add(link.toJson());
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء الرابط التسويقي: $e');
      return false;
    }
  }

  /// تحديث إحصائيات الرابط (النقرات والتحويلات)
  Future<bool> updateLinkStats(String linkId,
      {int? clickCount, int? conversionCount, double? totalEarnings}) async {
    try {
      final updates = <String, dynamic>{};
      if (clickCount != null) updates['clickCount'] = clickCount;
      if (conversionCount != null) updates['conversionCount'] = conversionCount;
      if (totalEarnings != null) updates['totalEarnings'] = totalEarnings;

      await _firestore
          .collection('affiliate_links')
          .doc(linkId)
          .update(updates);
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث إحصائيات الرابط: $e');
      return false;
    }
  }

  /// تسجيل نقرة على رابط تسويقي
  Future<bool> recordClick(ClickTracking click) async {
    try {
      await _firestore.collection('click_tracking').add(click.toJson());

      // تحديث عداد النقرات في الرابط
      final linkDoc = await _firestore
          .collection('affiliate_links')
          .doc(click.linkId)
          .get();
      if (linkDoc.exists) {
        final currentClicks = (linkDoc.data()?['clickCount'] as int?) ?? 0;
        await updateLinkStats(click.linkId, clickCount: currentClicks + 1);
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تسجيل النقرة: $e');
      return false;
    }
  }

  // ==================== إدارة العمولات ====================

  /// جلب عمولات مسوق معين
  Future<List<Commission>> getAffiliateCommissions(String affiliateId) async {
    try {
      final snapshot = await _firestore
          .collection('commissions')
          .where('affiliateId', isEqualTo: affiliateId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Commission.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب العمولات: $e');
      return [];
    }
  }

  /// إنشاء عمولة جديدة
  Future<bool> createCommission(Commission commission) async {
    try {
      await _firestore.collection('commissions').add(commission.toJson());
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء العمولة: $e');
      return false;
    }
  }

  /// تحديث حالة العمولة
  Future<bool> updateCommissionStatus(
      String commissionId, CommissionStatus status,
      {DateTime? statusDate,
      String? paymentMethod,
      String? paymentReference}) async {
    try {
      final updates = <String, dynamic>{
        'status': status.toString(),
      };

      switch (status) {
        case CommissionStatus.approved:
          updates['approvedAt'] =
              (statusDate ?? DateTime.now()).toIso8601String();
          break;
        case CommissionStatus.processing:
          updates['processingAt'] =
              (statusDate ?? DateTime.now()).toIso8601String();
          break;
        case CommissionStatus.earned:
          updates['earnedAt'] =
              (statusDate ?? DateTime.now()).toIso8601String();
          break;
        case CommissionStatus.paid:
          updates['paidAt'] = (statusDate ?? DateTime.now()).toIso8601String();
          if (paymentMethod != null) {
            updates['paymentMethod'] = paymentMethod;
          }
          if (paymentReference != null) {
            updates['paymentReference'] = paymentReference;
          }
          break;
        default:
          break;
      }

      await _firestore
          .collection('commissions')
          .doc(commissionId)
          .update(updates);
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة العمولة: $e');
      return false;
    }
  }

  // ==================== طلبات التسويق ====================

  /// إنشاء طلب تسويق جديد
  Future<bool> createAffiliateRequest(AffiliateRequest request) async {
    try {
      await _firestore.collection('affiliate_requests').add(request.toJson());
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء طلب التسويق: $e');
      return false;
    }
  }

  /// جلب طلب تسويق لمستخدم معين
  Future<AffiliateRequest?> getUserAffiliateRequest(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('affiliate_requests')
          .where('userId', isEqualTo: userId)
          .orderBy('requestDate', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return AffiliateRequest.fromJson(
            {...snapshot.docs.first.data(), 'id': snapshot.docs.first.id});
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في جلب طلب التسويق: $e');
      return null;
    }
  }

  // ==================== الإحصائيات ====================

  /// جلب إحصائيات مسوق
  Future<Map<String, dynamic>> getAffiliateStats(String affiliateId) async {
    try {
      // جلب العمولات
      final commissionsSnapshot = await _firestore
          .collection('commissions')
          .where('affiliateId', isEqualTo: affiliateId)
          .get();

      double totalEarnings = 0.0;
      double pendingEarnings = 0.0;
      int paidCommissions = 0;

      for (var doc in commissionsSnapshot.docs) {
        final commission = Commission.fromJson({...doc.data(), 'id': doc.id});
        if (commission.isPaid) {
          totalEarnings += commission.commissionAmount;
          paidCommissions++;
        } else if (commission.isEarned) {
          pendingEarnings += commission.commissionAmount;
        }
      }

      // جلب الروابط
      final linksSnapshot = await _firestore
          .collection('affiliate_links')
          .where('affiliateId', isEqualTo: affiliateId)
          .get();

      int totalClicks = 0;
      int totalConversions = 0;

      for (var doc in linksSnapshot.docs) {
        final link = AffiliateLink.fromJson({...doc.data(), 'id': doc.id});
        totalClicks += link.clickCount;
        totalConversions += link.conversionCount;
      }

      return {
        'totalEarnings': totalEarnings,
        'pendingEarnings': pendingEarnings,
        'totalCommissions': commissionsSnapshot.docs.length,
        'paidCommissions': paidCommissions,
        'totalClicks': totalClicks,
        'totalConversions': totalConversions,
        'conversionRate':
            totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0.0,
        'totalLinks': linksSnapshot.docs.length,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المسوق: $e');
      return {};
    }
  }

  /// جلب أفضل المسوقين
  Future<List<Map<String, dynamic>>> getTopAffiliates({int limit = 10}) async {
    try {
      final commissionsSnapshot = await _firestore
          .collection('commissions')
          .where('status', isEqualTo: CommissionStatus.paid.toString())
          .get();

      final affiliateEarnings = <String, double>{};

      for (var doc in commissionsSnapshot.docs) {
        final affiliateId = doc.data()['affiliateId'] as String;
        final amount =
            (doc.data()['commissionAmount'] as num?)?.toDouble() ?? 0.0;
        affiliateEarnings[affiliateId] =
            (affiliateEarnings[affiliateId] ?? 0.0) + amount;
      }

      final sortedAffiliates = affiliateEarnings.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedAffiliates
          .take(limit)
          .map((entry) => {
                'affiliateId': entry.key,
                'totalEarnings': entry.value,
              })
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب أفضل المسوقين: $e');
      return [];
    }
  }

  // ==================== طلبات الصرف ====================

  /// إنشاء طلب صرف
  Future<bool> createPayoutRequest(String affiliateId, double amount) async {
    try {
      final payoutRequest = {
        'affiliateId': affiliateId,
        'amount': amount,
        'status': 'pending',
        'requestDate': DateTime.now().toIso8601String(),
        'createdAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('payout_requests').add(payoutRequest);
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء طلب الصرف: $e');
      return false;
    }
  }

  /// جلب طلبات الصرف لمسوق معين
  Future<List<Map<String, dynamic>>> getPayoutRequests(
      String affiliateId) async {
    try {
      final snapshot = await _firestore
          .collection('payout_requests')
          .where('affiliateId', isEqualTo: affiliateId)
          .orderBy('requestDate', descending: true)
          .get();

      return snapshot.docs.map((doc) => {...doc.data(), 'id': doc.id}).toList();
    } catch (e) {
      debugPrint('خطأ في جلب طلبات الصرف: $e');
      return [];
    }
  }

  // ==================== إدارة المسوقين ====================

  /// جلب جميع المسوقين
  Future<List<User>> getAllAffiliates() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: UserRole.affiliate.toString())
          .where('status', isEqualTo: UserStatus.active.toString())
          .get();

      return snapshot.docs.map((doc) => User.fromJson(doc.data())).toList();
    } catch (e) {
      debugPrint('خطأ في جلب المسوقين: $e');
      return [];
    }
  }

  /// جلب مسوق بالكود
  Future<User?> getAffiliateByCode(String affiliateCode) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('affiliateCode', isEqualTo: affiliateCode)
          .where('role', isEqualTo: UserRole.affiliate.toString())
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return User.fromJson(snapshot.docs.first.data());
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في البحث عن المسوق بالكود: $e');
      return null;
    }
  }

  /// جلب طلبات المسوق للمستخدم
  Future<List<AffiliateRequest>> getUserAffiliateRequests(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('affiliate_requests')
          .where('userId', isEqualTo: userId)
          .orderBy('requestDate', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => AffiliateRequest.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب طلبات المسوق: $e');
      return [];
    }
  }

  /// إرسال طلب انضمام للتسويق
  Future<bool> submitAffiliateRequest(AffiliateRequest request) async {
    try {
      await _firestore
          .collection('affiliate_requests')
          .doc(request.id)
          .set(request.toJson());
      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال طلب المسوق: $e');
      return false;
    }
  }
}
