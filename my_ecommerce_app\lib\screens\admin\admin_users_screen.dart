import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/admin_controller.dart';
import '../../models/user.dart';
import '../../config/desktop_config.dart';
import 'forms/add_edit_user_form.dart';

class AdminUsersScreen extends StatelessWidget {
  const AdminUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final adminController = Get.find<AdminController>();
    final isMobile = DesktopConfig.isMobileSize(context);
    final padding = DesktopConfig.getResponsivePadding(context);

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Builder(
                builder: (context) {
                  final titleFontSize = DesktopConfig.getTitleFontSize(context);
                  final buttonPadding = DesktopConfig.getButtonPadding(context);
                  final minButtonSize = DesktopConfig.getMinButtonSize(context);

                  return isMobile
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إدارة المستخدمين',
                              style: TextStyle(
                                fontSize: titleFontSize,
                                fontWeight: FontWeight.bold,
                                color: Colors.teal[700],
                              ),
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () =>
                                    Get.to(() => const AddEditUserForm()),
                                icon: const Icon(Icons.person_add, size: 20),
                                label: const Text('إضافة مستخدم جديد'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                  padding: buttonPadding,
                                  minimumSize: minButtonSize,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'إدارة المستخدمين',
                              style: TextStyle(
                                fontSize: titleFontSize,
                                fontWeight: FontWeight.bold,
                                color: Colors.teal[700],
                              ),
                            ),
                            ElevatedButton.icon(
                              onPressed: () =>
                                  Get.to(() => const AddEditUserForm()),
                              icon: const Icon(Icons.person_add, size: 18),
                              label: const Text('إضافة مستخدم جديد'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                padding: buttonPadding,
                                minimumSize: minButtonSize,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ],
                        );
                },
              ),

              const SizedBox(height: 24),

              // Statistics Cards
              _buildStatisticsCards(adminController),

              const SizedBox(height: 24),

              // Filters and Search
              _buildFiltersSection(adminController),

              const SizedBox(height: 24),

              // Users Table
              SizedBox(
                height: 400, // ارتفاع ثابت للجدول
                child: _buildUsersTable(adminController),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsCards(AdminController controller) {
    return Obx(() {
      final analytics = controller.dashboardAnalytics;

      return Builder(
        builder: (context) {
          final isMobile = DesktopConfig.isMobileSize(context);
          final gridColumns = DesktopConfig.getGridColumns(context);

          final stats = [
            {
              'title': 'إجمالي المستخدمين',
              'value': '${analytics['totalUsers']}',
              'icon': Icons.people,
              'color': Colors.blue,
            },
            {
              'title': 'العملاء',
              'value': '${analytics['totalCustomers']}',
              'icon': Icons.person,
              'color': Colors.green,
            },
            {
              'title': 'المسوقون',
              'value': '${analytics['totalAffiliates']}',
              'icon': Icons.group,
              'color': Colors.orange,
            },
            {
              'title': 'المديرون',
              'value': '${analytics['totalAdmins']}',
              'icon': Icons.admin_panel_settings,
              'color': Colors.red,
            },
            {
              'title': 'المستخدمون النشطون',
              'value': '${analytics['activeUsers']}',
              'icon': Icons.check_circle,
              'color': Colors.teal,
            },
          ];

          if (isMobile) {
            // تخطيط شبكي للهواتف
            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: gridColumns,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
              ),
              itemCount: stats.length,
              itemBuilder: (context, index) {
                final stat = stats[index];
                return _buildStatCard(
                  stat['title'] as String,
                  stat['value'] as String,
                  stat['icon'] as IconData,
                  stat['color'] as Color,
                );
              },
            );
          } else {
            // تخطيط صف للشاشات الكبيرة
            return Row(
              children: stats
                  .map((stat) => Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16),
                          child: _buildStatCard(
                            stat['title'] as String,
                            stat['value'] as String,
                            stat['icon'] as IconData,
                            stat['color'] as Color,
                          ),
                        ),
                      ))
                  .toList(),
            );
          }
        },
      );
    });
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Builder(
      builder: (context) {
        final isMobile = DesktopConfig.isMobileSize(context);
        final iconSize = DesktopConfig.getIconSize(context);

        return Card(
          elevation: 2,
          child: Container(
            padding: EdgeInsets.all(isMobile ? 16 : 12),
            height: DesktopConfig.getCardHeight(context),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: iconSize + 8, // أيقونة أكبر قليلاً
                ),
                SizedBox(height: isMobile ? 8 : 6),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: isMobile ? 20 : 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                SizedBox(height: isMobile ? 6 : 4),
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: isMobile ? 12 : 10,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFiltersSection(AdminController controller) {
    return Builder(
      builder: (context) {
        final isMobile = DesktopConfig.isMobileSize(context);
        final padding = DesktopConfig.getResponsivePadding(context);

        return Card(
          child: Padding(
            padding: padding,
            child: Column(
              children: [
                if (isMobile) ...[
                  // تخطيط عمودي للهواتف
                  // حقل البحث
                  TextField(
                    controller: controller.searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في المستخدمين',
                      hintText: 'ابحث بالاسم أو البريد الإلكتروني',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // صف الفلاتر
                  Row(
                    children: [
                      // فلتر الدور
                      Expanded(
                        child: Obx(() => DropdownButtonFormField<UserRole?>(
                              value: controller.selectedUserRole.value,
                              decoration: const InputDecoration(
                                labelText: 'الدور',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.filter_list),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                              ),
                              items: [
                                const DropdownMenuItem<UserRole?>(
                                  value: null,
                                  child: Text('الكل'),
                                ),
                                ...UserRole.values
                                    .map((role) => DropdownMenuItem(
                                          value: role,
                                          child: Text(_getRoleText(role)),
                                        )),
                              ],
                              onChanged: (value) {
                                controller.selectedUserRole.value = value;
                              },
                            )),
                      ),

                      const SizedBox(width: 12),

                      // فلتر الحالة
                      Expanded(
                        child: Obx(() => DropdownButtonFormField<UserStatus?>(
                              value: controller.selectedUserStatus.value,
                              decoration: const InputDecoration(
                                labelText: 'الحالة',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.toggle_on),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                              ),
                              items: [
                                const DropdownMenuItem<UserStatus?>(
                                  value: null,
                                  child: Text('الكل'),
                                ),
                                ...UserStatus.values
                                    .map((status) => DropdownMenuItem(
                                          value: status,
                                          child: Text(_getStatusText(status)),
                                        )),
                              ],
                              onChanged: (value) {
                                controller.selectedUserStatus.value = value;
                              },
                            )),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // زر مسح الفلاتر
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: controller.clearUserFilters,
                      icon: const Icon(Icons.clear),
                      label: const Text('مسح الفلاتر'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        foregroundColor: Colors.white,
                        padding: DesktopConfig.getButtonPadding(context),
                        minimumSize: DesktopConfig.getMinButtonSize(context),
                      ),
                    ),
                  ),
                ] else ...[
                  // تخطيط أفقي للشاشات الكبيرة
                  Row(
                    children: [
                      // Search Field
                      Expanded(
                        flex: 2,
                        child: TextField(
                          controller: controller.searchController,
                          decoration: const InputDecoration(
                            labelText: 'البحث في المستخدمين',
                            hintText:
                                'ابحث بالاسم أو البريد الإلكتروني أو رقم الهاتف',
                            prefixIcon: Icon(Icons.search),
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Role Filter
                      Expanded(
                        child: Obx(() => DropdownButtonFormField<UserRole?>(
                              value: controller.selectedUserRole.value,
                              decoration: const InputDecoration(
                                labelText: 'تصفية حسب الدور',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.filter_list),
                              ),
                              items: [
                                const DropdownMenuItem<UserRole?>(
                                  value: null,
                                  child: Text('جميع الأدوار'),
                                ),
                                ...UserRole.values
                                    .map((role) => DropdownMenuItem(
                                          value: role,
                                          child: Text(_getRoleText(role)),
                                        )),
                              ],
                              onChanged: (value) {
                                controller.selectedUserRole.value = value;
                              },
                            )),
                      ),

                      const SizedBox(width: 16),

                      // Status Filter
                      Expanded(
                        child: Obx(() => DropdownButtonFormField<UserStatus?>(
                              value: controller.selectedUserStatus.value,
                              decoration: const InputDecoration(
                                labelText: 'تصفية حسب الحالة',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.toggle_on),
                              ),
                              items: [
                                const DropdownMenuItem<UserStatus?>(
                                  value: null,
                                  child: Text('جميع الحالات'),
                                ),
                                ...UserStatus.values
                                    .map((status) => DropdownMenuItem(
                                          value: status,
                                          child: Text(_getStatusText(status)),
                                        )),
                              ],
                              onChanged: (value) {
                                controller.selectedUserStatus.value = value;
                              },
                            )),
                      ),

                      const SizedBox(width: 16),

                      // Clear Filters Button
                      ElevatedButton.icon(
                        onPressed: controller.clearUserFilters,
                        icon: const Icon(Icons.clear),
                        label: const Text('مسح الفلاتر'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUsersTable(AdminController controller) {
    return Obx(() {
      final users = controller.users;

      if (controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (users.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.people_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد مستخدمين',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return Builder(
        builder: (context) {
          final isMobile = DesktopConfig.isMobileSize(context);

          if (isMobile) {
            // تخطيط قائمة للهواتف
            return _buildMobileUsersList(users, controller);
          } else {
            // تخطيط جدول للشاشات الكبيرة
            return _buildDesktopUsersTable(users, controller);
          }
        },
      );
    });
  }

  Widget _buildMobileUsersList(List<User> users, AdminController controller) {
    return ListView.separated(
      itemCount: users.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final user = users[index];
        return _buildMobileUserCard(user, controller);
      },
    );
  }

  Widget _buildMobileUserCard(User user, AdminController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الصف الأول: الصورة والاسم والحالة
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: _getRoleColor(user.role),
                  backgroundImage: user.profileImageUrl != null
                      ? NetworkImage(user.profileImageUrl!)
                      : null,
                  child: user.profileImageUrl == null
                      ? Text(
                          user.firstName[0].toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.fullName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.email,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      if (user.phoneNumber != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          user.phoneNumber!,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Chip(
                  label: Text(_getStatusText(user.status)),
                  backgroundColor:
                      _getStatusColor(user.status).withValues(alpha: 0.1),
                  labelStyle: TextStyle(
                    color: _getStatusColor(user.status),
                    fontSize: 12,
                  ),
                  avatar: Icon(
                    _getStatusIcon(user.status),
                    color: _getStatusColor(user.status),
                    size: 16,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // الصف الثاني: الدور وكود المسوق وتاريخ التسجيل
            Row(
              children: [
                Chip(
                  label: Text(_getRoleText(user.role)),
                  backgroundColor: _getRoleColor(user.role).withValues(alpha: 0.1),
                  labelStyle: TextStyle(
                    color: _getRoleColor(user.role),
                    fontSize: 12,
                  ),
                  avatar: Icon(
                    _getRoleIcon(user.role),
                    color: _getRoleColor(user.role),
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                if (user.isAffiliate && user.affiliateCode != null) ...[
                  Chip(
                    label: Text('كود: ${user.affiliateCode}'),
                    backgroundColor: Colors.purple.withValues(alpha: 0.1),
                    labelStyle: const TextStyle(
                      color: Colors.purple,
                      fontSize: 11,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    'انضم: ${user.createdAt.day}/${user.createdAt.month}/${user.createdAt.year}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // الصف الثالث: الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => Get.to(() => AddEditUserForm(user: user)),
                    icon: const Icon(Icons.edit, size: 18),
                    label: const Text('تعديل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => user.isActive
                        ? controller.suspendUser(user.id)
                        : controller.activateUser(user.id),
                    icon: Icon(
                      user.isActive ? Icons.block : Icons.check_circle,
                      size: 18,
                    ),
                    label: Text(user.isActive ? 'إيقاف' : 'تفعيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          user.isActive ? Colors.orange : Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showDeleteConfirmation(controller, user),
                    icon: const Icon(Icons.delete, size: 18),
                    label: const Text('حذف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopUsersTable(List<User> users, AdminController controller) {
    return Card(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          headingRowColor: WidgetStateProperty.all(Colors.grey[100]),
          columns: const [
            DataColumn(label: Text('الصورة')),
            DataColumn(label: Text('الاسم')),
            DataColumn(label: Text('البريد الإلكتروني')),
            DataColumn(label: Text('رقم الهاتف')),
            DataColumn(label: Text('الدور')),
            DataColumn(label: Text('الحالة')),
            DataColumn(label: Text('تاريخ التسجيل')),
            DataColumn(label: Text('الإجراءات')),
          ],
          rows: users
              .map((user) => DataRow(
                    cells: [
                      // Profile Image
                      DataCell(
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: _getRoleColor(user.role),
                          backgroundImage: user.profileImageUrl != null
                              ? NetworkImage(user.profileImageUrl!)
                              : null,
                          child: user.profileImageUrl == null
                              ? Text(
                                  user.firstName[0].toUpperCase(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              : null,
                        ),
                      ),

                      // Name
                      DataCell(
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              user.fullName,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            if (user.isAffiliate && user.affiliateCode != null)
                              Text(
                                'كود المسوق: ${user.affiliateCode}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Email
                      DataCell(Text(user.email)),

                      // Phone
                      DataCell(Text(user.phoneNumber ?? 'غير محدد')),

                      // Role
                      DataCell(
                        Chip(
                          label: Text(_getRoleText(user.role)),
                          backgroundColor:
                              _getRoleColor(user.role).withValues(alpha: 0.1),
                          labelStyle:
                              TextStyle(color: _getRoleColor(user.role)),
                          avatar: Icon(
                            _getRoleIcon(user.role),
                            color: _getRoleColor(user.role),
                            size: 16,
                          ),
                        ),
                      ),

                      // Status
                      DataCell(
                        Chip(
                          label: Text(_getStatusText(user.status)),
                          backgroundColor:
                              _getStatusColor(user.status).withValues(alpha: 0.1),
                          labelStyle:
                              TextStyle(color: _getStatusColor(user.status)),
                          avatar: Icon(
                            _getStatusIcon(user.status),
                            color: _getStatusColor(user.status),
                            size: 16,
                          ),
                        ),
                      ),

                      // Created Date
                      DataCell(
                        Text(
                          '${user.createdAt.day}/${user.createdAt.month}/${user.createdAt.year}',
                        ),
                      ),

                      // Actions
                      DataCell(
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: () =>
                                  Get.to(() => AddEditUserForm(user: user)),
                              icon: const Icon(Icons.edit, size: 20),
                              tooltip: 'تعديل',
                              color: Colors.blue,
                            ),
                            if (user.isActive)
                              IconButton(
                                onPressed: () =>
                                    controller.suspendUser(user.id),
                                icon: const Icon(Icons.block, size: 20),
                                tooltip: 'إيقاف',
                                color: Colors.orange,
                              )
                            else
                              IconButton(
                                onPressed: () =>
                                    controller.activateUser(user.id),
                                icon: const Icon(Icons.check_circle, size: 20),
                                tooltip: 'تفعيل',
                                color: Colors.green,
                              ),
                            IconButton(
                              onPressed: () =>
                                  _showDeleteConfirmation(controller, user),
                              icon: const Icon(Icons.delete, size: 20),
                              tooltip: 'حذف',
                              color: Colors.red,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ))
              .toList(),
        ),
      ),
    );
  }

  // Helper methods
  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'مدير';
      case UserRole.affiliate:
        return 'مسوق';
      case UserRole.customer:
        return 'عميل';
    }
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Colors.red;
      case UserRole.affiliate:
        return Colors.orange;
      case UserRole.customer:
        return Colors.blue;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Icons.admin_panel_settings;
      case UserRole.affiliate:
        return Icons.group;
      case UserRole.customer:
        return Icons.person;
    }
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return 'نشط';
      case UserStatus.inactive:
        return 'غير نشط';
      case UserStatus.suspended:
        return 'موقوف';
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Colors.green;
      case UserStatus.inactive:
        return Colors.grey;
      case UserStatus.suspended:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Icons.check_circle;
      case UserStatus.inactive:
        return Icons.pause_circle;
      case UserStatus.suspended:
        return Icons.block;
    }
  }

  void _showDeleteConfirmation(AdminController controller, User user) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\n\n'
          'هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بهذا المستخدم.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteUser(user.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
