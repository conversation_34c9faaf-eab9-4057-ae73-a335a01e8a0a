import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'cart_service.dart';
import 'package:firebase_auth/firebase_auth.dart' as fb_auth;
import 'package:cloud_firestore/cloud_firestore.dart';

class AuthService extends GetxService {
  static AuthService get instance => Get.find();

  // Observable للمستخدم الحالي
  final Rx<User?> _currentUser = Rx<User?>(null);
  User? get currentUser => _currentUser.value;

  // حالة تسجيل الدخول
  final RxBool _isLoggedIn = false.obs;
  bool get isLoggedIn => _isLoggedIn.value;

  // حالة التحميل
  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    _loadUserFromStorage();
  }

  // تحميل بيانات المستخدم من التخزين المحلي
  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');

      if (userJson != null) {
        final userData = json.decode(userJson);
        _currentUser.value = User.fromJson(userData);
        _isLoggedIn.value = true;
      }
    } catch (e) {
      debugPrint('Error loading user from storage: $e');
    }
  }

  // حفظ بيانات المستخدم في التخزين المحلي
  Future<void> _saveUserToStorage(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = json.encode(user.toJson());
      await prefs.setString('current_user', userJson);
    } catch (e) {
      debugPrint('Error saving user to storage: $e');
    }
  }

  // تسجيل الدخول باستخدام Firebase Authentication
  Future<bool> login(String email, String password) async {
    try {
      debugPrint('بدأت عملية تسجيل الدخول');
      _isLoading.value = true;
      final credential = await fb_auth.FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password);
      final uid = credential.user?.uid;
      if (uid != null) {
        debugPrint('جلب بيانات المستخدم من Firestore...');
        try {
          final doc = await FirebaseFirestore.instance
              .collection('users')
              .doc(uid)
              .get()
              .timeout(const Duration(seconds: 8));
          debugPrint('تم جلب بيانات المستخدم من Firestore: ${doc.exists}');
          if (doc.exists) {
            final userData = doc.data()!;
            _currentUser.value = User.fromJson(userData);
            _isLoggedIn.value = true;
            await _saveUserToStorage(_currentUser.value!);
            await _initializeUserCart();
            Get.snackbar(
                'نجح تسجيل الدخول', 'مرحباً ${_currentUser.value!.fullName}',
                snackPosition: SnackPosition.TOP);
            return true;
          } else {
            Get.snackbar('خطأ', 'لم يتم العثور على بيانات المستخدم',
                snackPosition: SnackPosition.TOP);
            return false;
          }
        } on TimeoutException catch (_) {
          debugPrint('Timeout: Firestore لم يستجب خلال 8 ثواني (login)');
          Get.snackbar('خطأ في الاتصال', 'انتهت مهلة الاتصال بقاعدة البيانات',
              snackPosition: SnackPosition.TOP);
          return false;
        }
      } else {
        Get.snackbar('خطأ', 'تعذر تسجيل الدخول',
            snackPosition: SnackPosition.TOP);
        return false;
      }
    } on fb_auth.FirebaseAuthException catch (e) {
      Get.snackbar('خطأ في تسجيل الدخول', e.message ?? 'حدث خطأ',
          snackPosition: SnackPosition.TOP);
      return false;
    } catch (e) {
      if (e.toString().contains('unavailable') ||
          e.toString().contains('offline')) {
        debugPrint('Firestore غير متوفر أو الجهاز غير متصل بالإنترنت: $e');
        Get.snackbar(
            'خطأ في الاتصال', 'لا يوجد اتصال بالإنترنت أو Firestore غير متوفر',
            snackPosition: SnackPosition.TOP);
      } else {
        debugPrint('حدث خطأ أثناء تسجيل الدخول: $e');
        Get.snackbar('خطأ', 'حدث خطأ أثناء تسجيل الدخول: $e',
            snackPosition: SnackPosition.TOP);
      }
      return false;
    } finally {
      debugPrint('تم تنفيذ finally في login, سيتم إيقاف التحميل');
      _isLoading.value = false;
    }
  }

  // إنشاء حساب جديد باستخدام Firebase Authentication وFirestore
  Future<bool> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phoneNumber,
    required UserRole role,
    String? referralCode,
  }) async {
    try {
      _isLoading.value = true;
      // إنشاء مستخدم في Firebase Auth
      final credential = await fb_auth.FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);
      final uid = credential.user?.uid;
      if (uid == null) {
        Get.snackbar('خطأ', 'تعذر إنشاء الحساب',
            snackPosition: SnackPosition.TOP);
        return false;
      }
      // إنشاء مستخدم جديد في Firestore
      final newUser = User(
        id: uid,
        email: email,
        password: '', // لا تحفظ كلمة المرور
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        role: role,
        createdAt: DateTime.now(),
        affiliateCode: role == UserRole.affiliate
            ? _generateAffiliateCode(firstName, lastName)
            : null,
        referredBy: referralCode,
      );
      print('سيتم إنشاء مستخدم جديد في Firestore...');
      try {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(uid)
            .set(newUser.toJson())
            .timeout(const Duration(seconds: 8));
        print('تم إنشاء المستخدم في Firestore بنجاح');
      } on TimeoutException catch (_) {
        print('Timeout: Firestore لم يستجب خلال 8 ثواني');
        Get.snackbar('خطأ في الاتصال', 'انتهت مهلة الاتصال بقاعدة البيانات',
            snackPosition: SnackPosition.TOP);
        return false;
      }
      _currentUser.value = newUser;
      _isLoggedIn.value = true;
      await _saveUserToStorage(newUser);
      Get.snackbar('تم إنشاء الحساب بنجاح', 'مرحباً ${newUser.fullName}',
          snackPosition: SnackPosition.TOP);
      return true;
    } on fb_auth.FirebaseAuthException catch (e) {
      String errorMsg = e.message ?? 'حدث خطأ';
      if (e.code == 'email-already-in-use') {
        errorMsg = 'البريد الإلكتروني مستخدم مسبقاً';
      }
      print(
          'FirebaseAuthException عند التسجيل: code=${e.code}, message=${e.message}');
      Get.snackbar('خطأ في التسجيل', errorMsg,
          snackPosition: SnackPosition.TOP);
      return false;
    } catch (e) {
      if (e.toString().contains('unavailable') ||
          e.toString().contains('offline')) {
        print('Firestore غير متوفر أو الجهاز غير متصل بالإنترنت: $e');
        Get.snackbar(
            'خطأ في الاتصال', 'لا يوجد اتصال بالإنترنت أو Firestore غير متوفر',
            snackPosition: SnackPosition.TOP);
      } else {
        print('Exception عند التسجيل: $e');
        Get.snackbar('خطأ', 'حدث خطأ أثناء إنشاء الحساب: $e',
            snackPosition: SnackPosition.TOP);
      }
      return false;
    } finally {
      print('تم تنفيذ finally في register, سيتم إيقاف التحميل');
      _isLoading.value = false;
    }
  }

  // تسجيل الخروج باستخدام Firebase Auth
  Future<void> logout() async {
    try {
      _isLoading.value = true;
      await fb_auth.FirebaseAuth.instance.signOut();
      await _clearUserCart();
      _currentUser.value = null;
      _isLoggedIn.value = false;
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      Get.snackbar('تم تسجيل الخروج', 'تم تسجيل خروجك بنجاح',
          snackPosition: SnackPosition.TOP);
    } catch (e) {
      print('Error during logout: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  // إعادة تعيين كلمة المرور باستخدام Firebase Auth
  Future<bool> resetPassword(String email) async {
    try {
      _isLoading.value = true;
      await fb_auth.FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      Get.snackbar('تم إرسال رابط إعادة التعيين', 'تحقق من بريدك الإلكتروني',
          snackPosition: SnackPosition.TOP);
      return true;
    } on fb_auth.FirebaseAuthException catch (e) {
      Get.snackbar('خطأ', e.message ?? 'حدث خطأ أثناء إعادة تعيين كلمة المرور',
          snackPosition: SnackPosition.TOP);
      return false;
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء إعادة تعيين كلمة المرور: $e',
          snackPosition: SnackPosition.TOP);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // لم يعد هناك حاجة لدوال البحث المحلية، كل شيء عبر Firebase

  // إنشاء كود مسوق فريد
  String _generateAffiliateCode(String firstName, String lastName) {
    final timestamp =
        DateTime.now().millisecondsSinceEpoch.toString().substring(8);
    final initials = '${firstName[0]}${lastName[0]}'.toUpperCase();
    return '$initials$timestamp';
  }

  // تهيئة سلة التسوق للمستخدم
  Future<void> _initializeUserCart() async {
    try {
      if (_currentUser.value != null) {
        final cartService = Get.find<CartService>();
        await cartService.onUserLogin(_currentUser.value!.id);
      }
    } catch (e) {
      print('Error initializing user cart: $e');
    }
  }

  // تنظيف سلة التسوق
  Future<void> _clearUserCart() async {
    try {
      final cartService = Get.find<CartService>();
      if (_currentUser.value != null) {
        await cartService.onUserLogout(_currentUser.value!.id);
      }
    } catch (e) {
      print('Error clearing user cart: $e');
    }
  }

  // تحويل العميل إلى مسوق (يجب تنفيذها عبر Firestore في المستقبل)
  Future<bool> convertToAffiliate(String userId) async {
    // ملاحظة: يجب تنفيذ منطق التحويل عبر Firestore لاحقاً
    return false;
  }

  // التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // التحقق من قوة كلمة المرور
  bool isValidPassword(String password) {
    return password.length >= 6;
  }

  // الحصول على جميع المستخدمين (للمدير فقط) - يجب تنفيذها عبر Firestore لاحقاً
  List<User> getAllUsers() {
    return [];
  }
}

// تم حذف البيانات التجريبية، كل شيء عبر Firebase
