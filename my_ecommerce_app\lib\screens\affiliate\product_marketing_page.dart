import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../models/product.dart';
import '../../controllers/affiliate_controller.dart';

class ProductMarketingPage extends StatelessWidget {
  final Product product;

  const ProductMarketingPage({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final AffiliateController controller = Get.find<AffiliateController>();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('تسويق: ${product.name}'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareProduct(controller),
            tooltip: 'مشاركة المنتج',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // معلومات المنتج الأساسية
            _buildProductOverview(),

            // إحصائيات التسويق
            _buildMarketingStats(controller),

            // نماذج التسويق الجاهزة
            _buildMarketingTemplates(controller),

            // نصائح التسويق
            _buildMarketingTips(),

            // أدوات التسويق
            _buildMarketingTools(controller),

            const SizedBox(height: 100), // مساحة للـ FAB
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _generateAffiliateLink(controller),
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.link),
        label: const Text('إنشاء رابط تسويقي'),
      ),
    );
  }

  Widget _buildProductOverview() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صور المنتج
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: DecorationImage(
                  image: NetworkImage(product.imageUrls.isNotEmpty
                      ? product.imageUrls.first
                      : 'https://via.placeholder.com/300'),
                  fit: BoxFit.cover,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // اسم المنتج والسعر
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    product.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '\$${product.price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // الوصف
            Text(
              product.description,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
                height: 1.5,
              ),
            ),

            const SizedBox(height: 16),

            // المواصفات الرئيسية
            _buildKeyFeatures(),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyFeatures() {
    // مواصفات افتراضية للنظارات (يمكن تخصيصها حسب نوع المنتج)
    final features = [
      {
        'icon': Icons.visibility,
        'title': 'حماية UV400',
        'desc': 'حماية كاملة من الأشعة فوق البنفسجية'
      },
      {
        'icon': Icons.palette,
        'title': 'تصميم عصري',
        'desc': 'يناسب جميع الأوقات والمناسبات'
      },
      {
        'icon': Icons.build,
        'title': 'جودة عالية',
        'desc': 'مواد متينة وخفيفة الوزن'
      },
      {
        'icon': Icons.local_shipping,
        'title': 'شحن مجاني',
        'desc': 'توصيل سريع لجميع المدن'
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المميزات الرئيسية:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...features
            .map<Widget>((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        feature['icon'] as IconData,
                        color: Colors.teal,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              feature['title'] as String,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              feature['desc'] as String,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ))
,
      ],
    );
  }

  Widget _buildMarketingStats(AffiliateController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات التسويق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'العمولة المتوقعة',
                    '\$${(product.price * 0.10).toStringAsFixed(2)}',
                    '10% من السعر',
                    Colors.green,
                    Icons.monetization_on,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'معدل التحويل',
                    '8.5%',
                    'متوسط الفئة',
                    Colors.blue,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'شعبية المنتج',
                    'عالية',
                    'مبيعات ممتازة',
                    Colors.orange,
                    Icons.star,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'صعوبة البيع',
                    'سهل',
                    'منتج مطلوب',
                    Colors.purple,
                    Icons.thumb_up,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String title, String value, String subtitle, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMarketingTemplates(AffiliateController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نماذج تسويقية جاهزة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // نموذج للسوشيال ميديا
            _buildTemplateCard(
              'نموذج السوشيال ميديا',
              _getSocialMediaTemplate(),
              Icons.share,
              Colors.blue,
            ),

            const SizedBox(height: 12),

            // نموذج الواتساب
            _buildTemplateCard(
              'نموذج الواتساب',
              _getWhatsAppTemplate(),
              Icons.message,
              Colors.green,
            ),

            const SizedBox(height: 12),

            // نموذج الإيميل
            _buildTemplateCard(
              'نموذج الإيميل',
              _getEmailTemplate(),
              Icons.email,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateCard(
      String title, String template, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.copy, size: 18),
                  onPressed: () => _copyTemplate(template, title),
                  tooltip: 'نسخ النموذج',
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Text(
              template,
              style: const TextStyle(
                fontSize: 13,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarketingTips() {
    final tips = [
      {
        'icon': Icons.lightbulb,
        'title': 'استخدم صور جذابة',
        'desc': 'الصور عالية الجودة تزيد معدل التحويل بنسبة 40%'
      },
      {
        'icon': Icons.schedule,
        'title': 'اختر التوقيت المناسب',
        'desc': 'أفضل أوقات النشر: 8-10 صباحاً و 7-9 مساءً'
      },
      {
        'icon': Icons.group,
        'title': 'استهدف الجمهور المناسب',
        'desc': 'ركز على الأشخاص المهتمين بالموضة والأناقة'
      },
      {
        'icon': Icons.trending_up,
        'title': 'استخدم الهاشتاجات',
        'desc': '#نظارات #موضة #تسوق #عروض #خصم'
      },
    ];

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نصائح للتسويق الفعال',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...tips
                .map<Widget>((tip) => Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.teal.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              tip['icon'] as IconData,
                              color: Colors.teal,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  tip['title'] as String,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  tip['desc'] as String,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                    height: 1.3,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ))
,
          ],
        ),
      ),
    );
  }

  Widget _buildMarketingTools(AffiliateController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أدوات التسويق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // أزرار الأدوات
            Row(
              children: [
                Expanded(
                  child: _buildToolButton(
                    'تحميل الصور',
                    Icons.download,
                    Colors.blue,
                    () => _downloadImages(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildToolButton(
                    'إنشاء بانر',
                    Icons.image,
                    Colors.purple,
                    () => _createBanner(),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildToolButton(
                    'مشاركة سريعة',
                    Icons.share,
                    Colors.green,
                    () => _quickShare(controller),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildToolButton(
                    'إحصائيات مفصلة',
                    Icons.analytics,
                    Colors.orange,
                    () => _showDetailedStats(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolButton(
      String title, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(
        title,
        style: const TextStyle(fontSize: 12),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  // النماذج التسويقية
  String _getSocialMediaTemplate() {
    return '''🔥 عرض خاص لفترة محدودة! 🔥

✨ ${product.name} ✨
💰 بسعر مميز: \$${product.price.toStringAsFixed(2)} فقط!

🌟 المميزات:
• حماية كاملة من الأشعة فوق البنفسجية
• تصميم عصري وأنيق
• جودة عالية ومتانة فائقة
• شحن مجاني لجميع المدن

🛒 اطلب الآن واحصل على خصم إضافي!
👆 اضغط على الرابط للطلب

#نظارات #موضة #تسوق #عروض #خصم''';
  }

  String _getWhatsAppTemplate() {
    return '''السلام عليكم ورحمة الله وبركاته 👋

أقدم لك منتج رائع بسعر مميز:

🕶️ *${product.name}*
💵 السعر: *\$${product.price.toStringAsFixed(2)}*

✅ المميزات:
• حماية UV400 كاملة
• تصميم عصري يناسب الجميع  
• جودة عالية ومواد متينة
• ضمان الجودة والاستبدال

🚚 *شحن مجاني* لجميع المدن
⏰ *توصيل سريع* خلال 2-3 أيام

للطلب اضغط على الرابط أدناه:
[رابط المنتج]

أي استفسار أنا في الخدمة 😊''';
  }

  String _getEmailTemplate() {
    return '''الموضوع: عرض خاص - ${product.name} بسعر مميز!

عزيزي العميل،

نتشرف بتقديم منتج مميز لك:

${product.name}
السعر الخاص: \$${product.price.toStringAsFixed(2)}

وصف المنتج:
${product.description}

المميزات الرئيسية:
• حماية كاملة من الأشعة فوق البنفسجية
• تصميم عصري وأنيق
• جودة عالية ومتانة فائقة
• ضمان الجودة

العرض الخاص:
✓ شحن مجاني
✓ ضمان الاستبدال
✓ خدمة عملاء 24/7

للطلب الآن، اضغط على الرابط أدناه:
[رابط المنتج]

مع تحياتنا،
فريق المبيعات''';
  }

  // الوظائف المساعدة
  void _copyTemplate(String template, String templateName) {
    Clipboard.setData(ClipboardData(text: template));
    Get.snackbar(
      'تم النسخ',
      'تم نسخ $templateName إلى الحافظة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  void _shareProduct(AffiliateController controller) {
    // منطق مشاركة المنتج
    Get.snackbar(
      'مشاركة',
      'تم فتح خيارات المشاركة',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _generateAffiliateLink(AffiliateController controller) {
    // منطق إنشاء رابط تسويقي
    controller.createProductLink(product.id.toString());
    Get.snackbar(
      'تم الإنشاء',
      'تم إنشاء رابط تسويقي للمنتج',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  void _downloadImages() {
    Get.snackbar(
      'تحميل الصور',
      'سيتم تحميل صور المنتج قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _createBanner() {
    Get.snackbar(
      'إنشاء بانر',
      'سيتم فتح أداة إنشاء البانر قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _quickShare(AffiliateController controller) {
    Get.snackbar(
      'مشاركة سريعة',
      'تم فتح خيارات المشاركة السريعة',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _showDetailedStats() {
    Get.snackbar(
      'الإحصائيات',
      'سيتم عرض الإحصائيات المفصلة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
