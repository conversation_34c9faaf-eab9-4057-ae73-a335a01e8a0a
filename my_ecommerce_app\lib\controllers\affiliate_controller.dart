import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../models/user.dart';
import '../models/commission.dart';
import '../models/affiliate_link.dart';
import '../models/product.dart';
import '../services/affiliate_service.dart';
import '../services/auth_service.dart';

class AffiliateController extends GetxController
    with GetTickerProviderStateMixin {
  final AffiliateService _affiliateService = Get.find<AffiliateService>();
  final AuthService _authService = Get.find<AuthService>();

  // Tab Controller
  late TabController tabController;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList<Commission> commissions = <Commission>[].obs;
  final RxList<AffiliateLink> affiliateLinks = <AffiliateLink>[].obs;
  final RxList<Product> availableProducts = <Product>[].obs;
  final RxMap<String, dynamic> stats = <String, dynamic>{}.obs;

  // إحصائيات مفصلة للصفحة الرئيسية
  final RxMap<String, dynamic> todayStats = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> monthStats = <String, dynamic>{}.obs;
  final RxList<Product> recommendedProducts = <Product>[].obs;
  final RxList<AffiliateLink> topPerformingLinks = <AffiliateLink>[].obs;
  final RxList<Commission> recentCommissions = <Commission>[].obs;

  // Form controllers for creating links
  final TextEditingController productUrlController = TextEditingController();
  final TextEditingController customUrlController = TextEditingController();
  final Rx<LinkType> selectedLinkType = LinkType.general.obs;

  // Filters
  final Rx<CommissionStatus?> statusFilter = Rx<CommissionStatus?>(null);
  final Rx<DateTimeRange?> dateFilter = Rx<DateTimeRange?>(null);

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 5, vsync: this);
    loadData();
  }

  @override
  void onClose() {
    tabController.dispose();
    productUrlController.dispose();
    customUrlController.dispose();
    super.onClose();
  }

  // تحميل البيانات
  Future<void> loadData() async {
    if (_authService.currentUser?.role != UserRole.affiliate) return;

    isLoading.value = true;

    try {
      final affiliateId = _authService.currentUser!.id;

      // تحميل العمولات
      commissions.value =
          _affiliateService.getAffiliateCommissions(affiliateId);

      // تحميل الروابط التسويقية
      affiliateLinks.value = _affiliateService.getAffiliateLinks(affiliateId);

      // تحميل الإحصائيات
      stats.value = _affiliateService.getAffiliateStats(affiliateId);

      // تحميل المنتجات المتاحة للتسويق
      availableProducts.value = _affiliateService.getAvailableProducts();

      // تحميل البيانات المفصلة للصفحة الرئيسية
      await loadHomePageData();
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحميل بيانات الصفحة الرئيسية المفصلة
  Future<void> loadHomePageData() async {
    final affiliateId = _authService.currentUser!.id;

    // تحميل إحصائيات اليوم
    await loadTodayStats();

    // تحميل إحصائيات الشهر
    await loadMonthStats();

    // تحميل المنتجات المقترحة للتسويق
    await loadRecommendedProducts();

    // تحميل أفضل الروابط أداءً
    await loadTopPerformingLinks();

    // تحميل العمولات الأخيرة
    await loadRecentCommissions();
  }

  // تحميل إحصائيات اليوم
  Future<void> loadTodayStats() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    // فلترة العمولات والروابط لليوم الحالي
    final todayCommissions = commissions.where((c) =>
      c.createdAt.isAfter(startOfDay) && c.createdAt.isBefore(endOfDay)
    ).toList();

    final todayClicks = affiliateLinks.fold(0, (sum, link) {
      // محاكاة نقرات اليوم (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
      return sum + (link.clickCount * 0.1).round(); // 10% من إجمالي النقرات
    });

    final todayEarnings = todayCommissions.fold(0.0, (sum, c) => sum + c.commissionAmount);
    final todaySales = todayCommissions.length;
    final todayConversionRate = todayClicks > 0 ? (todaySales / todayClicks) * 100 : 0.0;

    todayStats.value = {
      'todayClicks': todayClicks,
      'todayEarnings': todayEarnings,
      'todaySales': todaySales,
      'todayConversionRate': todayConversionRate,
    };
  }

  // تحميل إحصائيات الشهر
  Future<void> loadMonthStats() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);

    // فلترة العمولات للشهر الحالي
    final monthCommissions = commissions.where((c) =>
      c.createdAt.isAfter(startOfMonth) && c.createdAt.isBefore(endOfMonth)
    ).toList();

    final monthlyEarnings = monthCommissions.fold(0.0, (sum, c) => sum + c.commissionAmount);
    final newCustomers = monthCommissions.map((c) => c.customerId).toSet().length;

    // أفضل المنتجات أداءً هذا الشهر
    final productPerformance = <String, int>{};
    for (var commission in monthCommissions) {
      productPerformance[commission.productId] =
        (productPerformance[commission.productId] ?? 0) + 1;
    }

    final topProducts = productPerformance.entries
      .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // حساب معدل النمو (محاكاة)
    final lastMonthEarnings = monthlyEarnings * 0.85; // محاكاة الشهر السابق
    final growthRate = lastMonthEarnings > 0
      ? ((monthlyEarnings - lastMonthEarnings) / lastMonthEarnings) * 100
      : 0.0;

    monthStats.value = {
      'monthlyEarnings': monthlyEarnings,
      'newCustomers': newCustomers,
      'topProducts': topProducts.take(5).toList(),
      'growthRate': growthRate,
    };
  }

  // تحميل المنتجات المقترحة للتسويق
  Future<void> loadRecommendedProducts() async {
    // ترتيب المنتجات حسب العمولة والشعبية
    final sortedProducts = List<Product>.from(availableProducts);
    sortedProducts.sort((a, b) {
      // حساب نقاط الترشيح (عمولة + شعبية + تقييم)
      final scoreA = (a.price * 0.1) + (a.reviewCount * 0.5) + (a.rating * 10);
      final scoreB = (b.price * 0.1) + (b.reviewCount * 0.5) + (b.rating * 10);
      return scoreB.compareTo(scoreA);
    });

    recommendedProducts.value = sortedProducts.take(8).toList();
  }

  // تحميل أفضل الروابط أداءً
  Future<void> loadTopPerformingLinks() async {
    final sortedLinks = List<AffiliateLink>.from(affiliateLinks);
    sortedLinks.sort((a, b) => b.totalEarnings.compareTo(a.totalEarnings));
    topPerformingLinks.value = sortedLinks.take(5).toList();
  }

  // تحميل العمولات الأخيرة
  Future<void> loadRecentCommissions() async {
    final sortedCommissions = List<Commission>.from(commissions);
    sortedCommissions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    recentCommissions.value = sortedCommissions.take(10).toList();
  }

  // إنشاء رابط تسويقي جديد
  Future<void> createAffiliateLink() async {
    if (_authService.currentUser?.role != UserRole.affiliate) return;

    final user = _authService.currentUser!;

    try {
      isLoading.value = true;

      String? productId;
      String? customUrl;

      if (selectedLinkType.value == LinkType.product) {
        // استخراج معرف المنتج من الرابط
        productId = _extractProductIdFromUrl(productUrlController.text);
        if (productId == null) {
          Get.snackbar('خطأ', 'رابط المنتج غير صحيح');
          return;
        }
      } else if (selectedLinkType.value == LinkType.general) {
        customUrl = customUrlController.text.trim();
      }

      final link = await _affiliateService.createAffiliateLink(
        affiliateId: user.id,
        affiliateCode: user.affiliateCode!,
        type: selectedLinkType.value,
        productId: productId,
        customUrl: customUrl,
      );

      affiliateLinks.add(link);

      // تنظيف النماذج
      productUrlController.clear();
      customUrlController.clear();
      selectedLinkType.value = LinkType.general;

      Get.back(); // إغلاق الحوار
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء إنشاء الرابط: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // نسخ الرابط إلى الحافظة
  void copyLinkToClipboard(String url) {
    Clipboard.setData(ClipboardData(text: url));
    Get.snackbar(
      'تم النسخ',
      'تم نسخ الرابط إلى الحافظة',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  // مشاركة الرابط
  void shareLink(String url, String title) {
    // في التطبيق الحقيقي، استخدم share package
    copyLinkToClipboard(url);
    Get.snackbar(
      'مشاركة الرابط',
      'تم نسخ الرابط للمشاركة',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // طلب صرف العمولات
  Future<void> requestPayout() async {
    if (_authService.currentUser?.role != UserRole.affiliate) return;

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('طلب صرف العمولات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'المبلغ المستحق: \$${stats['pendingEarnings']?.toStringAsFixed(2) ?? '0.00'}'),
            const SizedBox(height: 8),
            const Text('هل تريد طلب صرف العمولات المستحقة؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (result == true) {
      isLoading.value = true;
      final success =
          await _affiliateService.requestPayout(_authService.currentUser!.id);
      isLoading.value = false;

      if (success) {
        loadData(); // إعادة تحميل البيانات
      }
    }
  }

  // تطبيق فلتر الحالة
  void applyStatusFilter(CommissionStatus? status) {
    statusFilter.value = status;
    _applyFilters();
  }

  // تطبيق فلتر التاريخ
  void applyDateFilter(DateTimeRange? dateRange) {
    dateFilter.value = dateRange;
    _applyFilters();
  }

  // تطبيق الفلاتر
  void _applyFilters() {
    if (_authService.currentUser?.role != UserRole.affiliate) return;

    var filteredCommissions =
        _affiliateService.getAffiliateCommissions(_authService.currentUser!.id);

    // فلتر الحالة
    if (statusFilter.value != null) {
      filteredCommissions = filteredCommissions
          .where((c) => c.status == statusFilter.value)
          .toList();
    }

    // فلتر التاريخ
    if (dateFilter.value != null) {
      filteredCommissions = filteredCommissions
          .where((c) =>
              c.createdAt.isAfter(dateFilter.value!.start) &&
              c.createdAt
                  .isBefore(dateFilter.value!.end.add(const Duration(days: 1))))
          .toList();
    }

    commissions.value = filteredCommissions;
  }

  // مسح الفلاتر
  void clearFilters() {
    statusFilter.value = null;
    dateFilter.value = null;
    loadData();
  }

  // استخراج معرف المنتج من الرابط
  String? _extractProductIdFromUrl(String url) {
    // تنفيذ بسيط لاستخراج معرف المنتج
    final regex = RegExp(r'/product/(\d+)');
    final match = regex.firstMatch(url);
    return match?.group(1);
  }

  // الحصول على نص حالة العمولة
  String getCommissionStatusText(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return 'معلقة';
      case CommissionStatus.approved:
        return 'معتمدة';
      case CommissionStatus.processing:
        return 'قيد المعالجة';
      case CommissionStatus.earned:
        return 'مستحقة';
      case CommissionStatus.paid:
        return 'مدفوعة';
      case CommissionStatus.cancelled:
        return 'ملغية';
      case CommissionStatus.refunded:
        return 'مسترجعة';
    }
  }

  // الحصول على لون حالة العمولة
  Color getCommissionStatusColor(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return Colors.orange;
      case CommissionStatus.approved:
        return Colors.blue;
      case CommissionStatus.processing:
        return Colors.purple;
      case CommissionStatus.earned:
        return Colors.green;
      case CommissionStatus.paid:
        return Colors.teal;
      case CommissionStatus.cancelled:
        return Colors.red;
      case CommissionStatus.refunded:
        return Colors.grey;
    }
  }

  // الحصول على نص نوع الرابط
  String getLinkTypeText(LinkType type) {
    switch (type) {
      case LinkType.product:
        return 'منتج محدد';
      case LinkType.category:
        return 'فئة منتجات';
      case LinkType.general:
        return 'عام';
    }
  }

  // إنشاء رابط تسويقي لمنتج محدد
  Future<void> createProductLink(String productId) async {
    if (_authService.currentUser?.role != UserRole.affiliate) return;

    final user = _authService.currentUser!;

    try {
      isLoading.value = true;

      final link = await _affiliateService.createAffiliateLink(
        affiliateId: user.id,
        affiliateCode: user.affiliateCode!,
        type: LinkType.product,
        productId: productId,
      );

      affiliateLinks.add(link);

      Get.snackbar(
        'تم الإنشاء',
        'تم إنشاء رابط تسويقي للمنتج بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء إنشاء الرابط: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // البحث في المنتجات
  void searchProducts(String query) {
    if (query.isEmpty) {
      availableProducts.value = _affiliateService.getAvailableProducts();
      return;
    }

    final allProducts = _affiliateService.getAvailableProducts();
    availableProducts.value = allProducts
        .where((product) =>
            product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  // فلترة المنتجات حسب الفئة
  void filterProductsByCategory(String? category) {
    final allProducts = _affiliateService.getAvailableProducts();

    if (category == null || category.isEmpty) {
      availableProducts.value = allProducts;
      return;
    }

    availableProducts.value = allProducts
        .where((product) =>
            product.category.toLowerCase() == category.toLowerCase())
        .toList();
  }

  // فلترة المنتجات حسب نطاق السعر
  void filterProductsByPriceRange(double minPrice, double maxPrice) {
    final allProducts = _affiliateService.getAvailableProducts();

    availableProducts.value = allProducts
        .where(
            (product) => product.price >= minPrice && product.price <= maxPrice)
        .toList();
  }

  // ترتيب المنتجات
  void sortProducts(String sortBy) {
    final products = List<Product>.from(availableProducts);

    switch (sortBy) {
      case 'price_low':
        products.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_high':
        products.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'name':
        products.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'commission':
        products.sort((a, b) => (b.price * 0.10).compareTo(a.price * 0.10));
        break;
      default:
        // الترتيب الافتراضي
        break;
    }

    availableProducts.value = products;
  }

  // الحصول على إحصائيات منتج محدد
  Map<String, dynamic> getProductStats(String productId) {
    // في التطبيق الحقيقي، سيتم جلب هذه البيانات من قاعدة البيانات
    return {
      'totalClicks': 150,
      'totalConversions': 12,
      'conversionRate': 8.0,
      'totalEarnings': 89.88,
      'averageOrderValue': 149.80,
    };
  }

  // إنشاء نموذج تسويقي مخصص
  String generateCustomMarketingTemplate(Product product, String templateType) {
    switch (templateType) {
      case 'social':
        return '''🔥 عرض خاص لفترة محدودة! 🔥

✨ ${product.name} ✨
💰 بسعر مميز: \$${product.price.toStringAsFixed(2)} فقط!

🌟 المميزات:
${product.description}

🛒 اطلب الآن واحصل على خصم إضافي!
👆 اضغط على الرابط للطلب

#نظارات #موضة #تسوق #عروض #خصم''';

      case 'whatsapp':
        return '''السلام عليكم ورحمة الله وبركاته 👋

أقدم لك منتج رائع بسعر مميز:

🕶️ *${product.name}*
💵 السعر: *\$${product.price.toStringAsFixed(2)}*

✅ الوصف:
${product.description}

🚚 *شحن مجاني* لجميع المدن
⏰ *توصيل سريع* خلال 2-3 أيام

للطلب اضغط على الرابط أدناه:
[رابط المنتج]

أي استفسار أنا في الخدمة 😊''';

      case 'email':
        return '''الموضوع: عرض خاص - ${product.name} بسعر مميز!

عزيزي العميل،

نتشرف بتقديم منتج مميز لك:

${product.name}
السعر الخاص: \$${product.price.toStringAsFixed(2)}

وصف المنتج:
${product.description}

العرض الخاص:
✓ شحن مجاني
✓ ضمان الاستبدال
✓ خدمة عملاء 24/7

للطلب الآن، اضغط على الرابط أدناه:
[رابط المنتج]

مع تحياتنا،
فريق المبيعات''';

      default:
        return 'نموذج تسويقي لـ ${product.name}';
    }
  }

  // Data loading methods for testing
  Future<void> loadAffiliateLinks() async {
    await loadData();
  }

  Future<void> loadCommissions() async {
    await loadData();
  }
}
