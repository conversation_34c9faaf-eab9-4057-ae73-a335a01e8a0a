import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/product.dart';
import '../../controllers/affiliate_controller.dart';
import 'product_marketing_page.dart';

class ProductsForMarketing extends StatelessWidget {
  const ProductsForMarketing({super.key});

  @override
  Widget build(BuildContext context) {
    final AffiliateController controller = Get.find<AffiliateController>();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('المنتجات المتاحة للتسويق'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context, controller),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context, controller),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات السريعة
          _buildQuickStats(controller),

          // قائمة المنتجات
          Expanded(
            child: Obx(() => controller.availableProducts.isEmpty
                ? _buildEmptyState()
                : _buildProductsList(controller)),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(AffiliateController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: _buildQuickStatCard(
              'المنتجات المتاحة',
              '${controller.availableProducts.length}',
              Icons.inventory,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickStatCard(
              'متوسط العمولة',
              '10%',
              Icons.percent,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickStatCard(
              'الأكثر مبيعاً',
              'نظارات شمسية',
              Icons.trending_up,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 11,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList(AffiliateController controller) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.availableProducts.length,
      itemBuilder: (context, index) {
        final product = controller.availableProducts[index];
        return _buildProductCard(product, controller);
      },
    );
  }

  Widget _buildProductCard(Product product, AffiliateController controller) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => Get.to(() => ProductMarketingPage(product: product)),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة المنتج
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: NetworkImage(product.imageUrls.isNotEmpty
                        ? product.imageUrls.first
                        : 'https://via.placeholder.com/150'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // معلومات المنتج
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    Text(
                      product.description,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '\$${product.price.toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'عمولة: \$${(product.price * 0.10).toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // مؤشرات الأداء
                    Row(
                      children: [
                        _buildPerformanceIndicator(
                          'شعبية',
                          _getPopularityLevel(product),
                          _getPopularityColor(product),
                        ),
                        const SizedBox(width: 12),
                        _buildPerformanceIndicator(
                          'سهولة البيع',
                          'عالية',
                          Colors.green,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // أزرار الإجراءات
              Column(
                children: [
                  IconButton(
                    onPressed: () =>
                        Get.to(() => ProductMarketingPage(product: product)),
                    icon: const Icon(Icons.campaign),
                    tooltip: 'صفحة التسويق',
                    color: Colors.teal,
                  ),
                  IconButton(
                    onPressed: () => _quickCreateLink(product, controller),
                    icon: const Icon(Icons.link),
                    tooltip: 'إنشاء رابط سريع',
                    color: Colors.blue,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceIndicator(String label, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label: $value',
          style: TextStyle(
            fontSize: 11,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات متاحة للتسويق',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة منتجات جديدة قريباً',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // الوظائف المساعدة
  String _getPopularityLevel(Product product) {
    // منطق تحديد مستوى الشعبية بناءً على المبيعات أو التقييمات
    if (product.price > 200) return 'عالية';
    if (product.price > 100) return 'متوسطة';
    return 'جيدة';
  }

  Color _getPopularityColor(Product product) {
    final level = _getPopularityLevel(product);
    switch (level) {
      case 'عالية':
        return Colors.green;
      case 'متوسطة':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  void _quickCreateLink(Product product, AffiliateController controller) {
    controller.createProductLink(product.id.toString());
    Get.snackbar(
      'تم الإنشاء',
      'تم إنشاء رابط تسويقي لـ ${product.name}',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  void _showSearchDialog(BuildContext context, AffiliateController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في المنتجات'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'ابحث عن منتج...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            // منطق البحث
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context, AffiliateController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية المنتجات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text('عمولة عالية (أكثر من 15%)'),
              value: false,
              onChanged: (value) {},
            ),
            CheckboxListTile(
              title: const Text('منتجات شائعة'),
              value: false,
              onChanged: (value) {},
            ),
            CheckboxListTile(
              title: const Text('سهلة البيع'),
              value: false,
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}
