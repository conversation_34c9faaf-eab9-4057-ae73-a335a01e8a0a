import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/search_controller.dart' as search_ctrl;
import '../../widgets/product_card.dart';

class AdvancedSearchScreen extends StatelessWidget {
  const AdvancedSearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final searchController = Get.put(search_ctrl.SearchController());

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // شريط البحث
            _buildSearchBar(searchController),

            // الاقتراحات
            Obx(() => searchController.suggestions.isNotEmpty
                ? _buildSuggestions(searchController)
                : const SizedBox()),

            // الفلاتر والترتيب
            _buildFiltersBar(searchController),

            // النتائج
            Expanded(
              child: Obx(() {
                if (searchController.isSearching.value) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        <PERSON><PERSON><PERSON><PERSON>(height: 16),
                        Text('جاري البحث...'),
                      ],
                    ),
                  );
                }

                if (searchController.searchQuery.value.isEmpty &&
                    !searchController.hasActiveFilters) {
                  return _buildSearchHome(searchController);
                }

                if (searchController.searchResults.isEmpty) {
                  return _buildNoResults(searchController);
                }

                return _buildSearchResults(searchController);
              }),
            ),
          ],
        ),
      ),
    );
  }

  /// شريط البحث
  Widget _buildSearchBar(search_ctrl.SearchController searchController) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الرجوع
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back),
          ),

          // حقل البحث
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextField(
                controller: searchController.searchTextController,
                decoration: InputDecoration(
                  hintText: 'ابحث عن المنتجات...',
                  prefixIcon: const Icon(Icons.search, color: Colors.grey),
                  suffixIcon:
                      Obx(() => searchController.searchQuery.value.isNotEmpty
                          ? IconButton(
                              onPressed: () => searchController.clearSearch(),
                              icon: const Icon(Icons.clear, color: Colors.grey),
                            )
                          : const SizedBox()),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 15,
                  ),
                ),
                onSubmitted: (value) => searchController.search(value),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // زر الفلاتر
          Obx(() => Stack(
                children: [
                  IconButton(
                    onPressed: () => searchController.showFilters.value =
                        !searchController.showFilters.value,
                    icon: Icon(
                      Icons.tune,
                      color: searchController.hasActiveFilters
                          ? Colors.blue
                          : Colors.grey,
                    ),
                  ),
                  if (searchController.activeFiltersCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          '${searchController.activeFiltersCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              )),
        ],
      ),
    );
  }

  /// الاقتراحات
  Widget _buildSuggestions(search_ctrl.SearchController searchController) {
    return Container(
      color: Colors.white,
      child: Column(
        children: searchController.suggestions.map((suggestion) {
          return ListTile(
            leading: const Icon(Icons.search, color: Colors.grey),
            title: Text(suggestion),
            onTap: () => searchController.searchWithSuggestion(suggestion),
          );
        }).toList(),
      ),
    );
  }

  /// شريط الفلاتر والترتيب
  Widget _buildFiltersBar(search_ctrl.SearchController searchController) {
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: searchController.showFilters.value ? null : 60,
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                // شريط الترتيب
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Text(
                        'النتائج: ${searchController.searchResults.length}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const Spacer(),

                      // ترتيب النتائج
                      DropdownButton<String>(
                        value: searchController.sortBy.value,
                        underline: const SizedBox(),
                        items: const [
                          DropdownMenuItem(
                              value: 'relevance', child: Text('الأكثر صلة')),
                          DropdownMenuItem(
                              value: 'price_low',
                              child: Text('السعر: الأقل أولاً')),
                          DropdownMenuItem(
                              value: 'price_high',
                              child: Text('السعر: الأعلى أولاً')),
                          DropdownMenuItem(
                              value: 'rating', child: Text('التقييم')),
                          DropdownMenuItem(
                              value: 'newest', child: Text('الأحدث')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            searchController.sortBy.value = value;
                            searchController.search();
                          }
                        },
                      ),
                    ],
                  ),
                ),

                // الفلاتر المتقدمة
                if (searchController.showFilters.value)
                  _buildAdvancedFilters(searchController),
              ],
            ),
          ),
        ));
  }

  /// الفلاتر المتقدمة
  Widget _buildAdvancedFilters(search_ctrl.SearchController searchController) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الفئة
          const Text('الفئة', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Obx(() => Wrap(
                spacing: 8,
                children: [
                  FilterChip(
                    label: const Text('الكل'),
                    selected: searchController.selectedCategory.value.isEmpty,
                    onSelected: (selected) {
                      if (selected) {
                        searchController.selectedCategory.value = '';
                        searchController.search();
                      }
                    },
                  ),
                  ...searchController.availableCategories.map((category) {
                    return FilterChip(
                      label: Text(category),
                      selected:
                          searchController.selectedCategory.value == category,
                      onSelected: (selected) {
                        searchController.selectedCategory.value =
                            selected ? category : '';
                        searchController.search();
                      },
                    );
                  }),
                ],
              )),

          const SizedBox(height: 16),

          // نطاق السعر
          const Text('نطاق السعر',
              style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Obx(() => RangeSlider(
                values: RangeValues(
                  searchController.minPrice.value,
                  searchController.maxPrice.value,
                ),
                min: 0,
                max: searchController.allProducts.isNotEmpty
                    ? searchController.allProducts
                        .map((p) => p.price)
                        .reduce((a, b) => a > b ? a : b)
                    : 10000,
                divisions: 20,
                labels: RangeLabels(
                  '${searchController.minPrice.value.toInt()} ر.س',
                  '${searchController.maxPrice.value.toInt()} ر.س',
                ),
                onChanged: (values) {
                  searchController.minPrice.value = values.start;
                  searchController.maxPrice.value = values.end;
                },
                onChangeEnd: (values) => searchController.search(),
              )),

          const SizedBox(height: 16),

          // التقييم الأدنى
          const Text('التقييم الأدنى',
              style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Obx(() => Row(
                children: List.generate(5, (index) {
                  return GestureDetector(
                    onTap: () {
                      searchController.minRating.value = (index + 1).toDouble();
                      searchController.search();
                    },
                    child: Icon(
                      Icons.star,
                      color: index < searchController.minRating.value
                          ? Colors.amber
                          : Colors.grey[300],
                    ),
                  );
                }),
              )),

          const SizedBox(height: 16),

          // خيارات إضافية
          Obx(() => Column(
                children: [
                  CheckboxListTile(
                    title: const Text('المنتجات المتاحة فقط'),
                    value: searchController.inStockOnly.value,
                    onChanged: (value) {
                      searchController.inStockOnly.value = value ?? false;
                      searchController.search();
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                  CheckboxListTile(
                    title: const Text('الشحن المجاني فقط'),
                    value: searchController.freeShippingOnly.value,
                    onChanged: (value) {
                      searchController.freeShippingOnly.value = value ?? false;
                      searchController.search();
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                  CheckboxListTile(
                    title: const Text('العروض والخصومات فقط'),
                    value: searchController.onSaleOnly.value,
                    onChanged: (value) {
                      searchController.onSaleOnly.value = value ?? false;
                      searchController.search();
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ],
              )),

          const SizedBox(height: 16),

          // أزرار التحكم
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => searchController.resetFilters(),
                  child: const Text('إعادة تعيين'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => searchController.applyFiltersAndSearch(),
                  child: const Text('تطبيق'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// الصفحة الرئيسية للبحث
  Widget _buildSearchHome(search_ctrl.SearchController searchController) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تاريخ البحث
          if (searchController.searchHistory.isNotEmpty) ...[
            const Text(
              'عمليات البحث الأخيرة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: searchController.searchHistory.map((query) {
                return GestureDetector(
                  onTap: () => searchController.searchWithSuggestion(query),
                  child: Chip(
                    label: Text(query),
                    onDeleted: () =>
                        searchController.searchHistory.remove(query),
                    deleteIcon: const Icon(Icons.close, size: 16),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => searchController.clearSearchHistory(),
              child: const Text('مسح التاريخ'),
            ),
            const SizedBox(height: 24),
          ],

          // اقتراحات البحث
          const Text(
            'اقتراحات البحث',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              'نظارات شمسية',
              'نظارات طبية',
              'عدسات لاصقة',
              'إطارات تيتانيوم',
              'نظارات رياضية',
              'نظارات أطفال',
            ].map((suggestion) {
              return ActionChip(
                label: Text(suggestion),
                onPressed: () =>
                    searchController.searchWithSuggestion(suggestion),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// عدم وجود نتائج
  Widget _buildNoResults(search_ctrl.SearchController searchController) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              searchController.searchQuery.value.isNotEmpty
                  ? 'لم نجد أي منتجات تطابق "${searchController.searchQuery.value}"'
                  : 'لا توجد منتجات تطابق الفلاتر المحددة',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => searchController.resetFilters(),
              child: const Text('إعادة تعيين الفلاتر'),
            ),
          ],
        ),
      ),
    );
  }

  /// نتائج البحث
  Widget _buildSearchResults(search_ctrl.SearchController searchController) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getCrossAxisCount(),
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: searchController.searchResults.length,
      itemBuilder: (context, index) {
        return ProductCard(
          product: searchController.searchResults[index],
          isGridView: true,
        );
      },
    );
  }

  /// تحديد عدد الأعمدة حسب حجم الشاشة
  int _getCrossAxisCount() {
    final width = Get.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2;
  }
}
