{"roots": ["my_ecommerce_app"], "packages": [{"name": "my_ecommerce_app", "version": "1.0.0+1", "dependencies": ["cached_network_image", "chewie", "cloud_firestore", "cupertino_icons", "firebase_auth", "firebase_core", "firebase_messaging", "flutter", "flutter_local_notifications", "flutter_svg", "get", "google_fonts", "http", "image_picker", "photo_view", "provider", "shared_preferences", "uuid", "video_player"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "cached_network_image", "version": "3.4.0", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "chewie", "version": "1.8.7", "dependencies": ["cupertino_icons", "flutter", "provider", "video_player", "wakelock_plus"]}, {"name": "video_player", "version": "2.9.2", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "photo_view", "version": "0.14.0", "dependencies": ["flutter"]}, {"name": "flutter_local_notifications", "version": "16.3.3", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "firebase_messaging", "version": "14.9.4", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "firebase_auth", "version": "4.20.0", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "cloud_firestore", "version": "4.17.5", "dependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_core", "version": "2.32.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "shared_preferences", "version": "2.2.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "flutter_svg", "version": "2.0.10+1", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "http", "version": "1.2.2", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "get", "version": "4.7.2", "dependencies": ["flutter", "web"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.0", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+1", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+1", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+1", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.5", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+1", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "wakelock_plus", "version": "1.2.8", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "video_player_web", "version": "2.3.2", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.2.3", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.6.5", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.4.14", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "timezone", "version": "0.9.4", "dependencies": ["path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "firebase_messaging_web", "version": "3.8.7", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.5.37", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "5.4.2", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_auth_web", "version": "5.12.0", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"]}, {"name": "firebase_auth_platform_interface", "version": "7.3.0", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "cloud_firestore_web", "version": "3.12.5", "dependencies": ["_flutterfire_internals", "cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "cloud_firestore_platform_interface", "version": "6.2.5", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.17.5", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "crypto", "version": "3.0.3", "dependencies": ["typed_data"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.2.2", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "path_provider", "version": "2.1.4", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "vector_graphics_compiler", "version": "1.1.11+1", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.11+1", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.11+1", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "web", "version": "0.5.1", "dependencies": []}, {"name": "http_parser", "version": "4.0.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "sqflite", "version": "2.3.3+1", "dependencies": ["flutter", "path", "sqflite_common"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "file_selector_windows", "version": "0.9.3+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "file_selector_macos", "version": "0.9.4+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "mime", "version": "1.0.6", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.19", "dependencies": ["flutter"]}, {"name": "package_info_plus", "version": "8.0.2", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "win32", "version": "5.5.0", "dependencies": ["ffi"]}, {"name": "wakelock_plus_platform_interface", "version": "1.2.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "ffi", "version": "2.1.3", "dependencies": []}, {"name": "_flutterfire_internals", "version": "1.3.35", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "typed_data", "version": "1.3.2", "dependencies": ["collection"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.4", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "sqflite_common", "version": "2.5.4", "dependencies": ["meta", "path", "synchronized"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "petitparser", "version": "6.0.2", "dependencies": ["meta"]}, {"name": "synchronized", "version": "3.1.0+1", "dependencies": []}], "configVersion": 1}