import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

/// Desktop-specific configuration for better mouse handling
class DesktopConfig {
  static bool get isDesktop {
    return defaultTargetPlatform == TargetPlatform.windows ||
           defaultTargetPlatform == TargetPlatform.macOS ||
           defaultTargetPlatform == TargetPlatform.linux;
  }

  static bool get isWindows => defaultTargetPlatform == TargetPlatform.windows;
  static bool get isMacOS => defaultTargetPlatform == TargetPlatform.macOS;
  static bool get isLinux => defaultTargetPlatform == TargetPlatform.linux;

  /// Get platform-specific scroll behavior
  static ScrollBehavior get scrollBehavior {
    if (kIsWeb) {
      return WebScrollBehavior();
    } else if (isDesktop) {
      return DesktopScrollBehavior();
    }
    return const MaterialScrollBehavior();
  }

  /// Get platform-specific theme adjustments
  static ThemeData adjustThemeForDesktop(ThemeData theme) {
    if (!isDesktop) return theme;

    return theme.copyWith(
      // Adjust hover colors for desktop
      hoverColor: theme.primaryColor.withValues(alpha: 0.04),
      focusColor: theme.primaryColor.withValues(alpha: 0.12),

      // Better button themes for desktop
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          minimumSize: const Size(120, 40),
        ),
      ),

      // Better text button themes for desktop
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          minimumSize: const Size(80, 36),
        ),
      ),

      // Better icon button themes for desktop
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          minimumSize: const Size(40, 40),
          padding: const EdgeInsets.all(8),
        ),
      ),
    );
  }

  /// Get desktop-specific window constraints
  static BoxConstraints get windowConstraints {
    return const BoxConstraints(
      minWidth: 800,
      minHeight: 600,
      maxWidth: double.infinity,
      maxHeight: double.infinity,
    );
  }

  /// Desktop-specific app bar height
  static double get appBarHeight => isDesktop ? 56.0 : kToolbarHeight;

  /// Desktop-specific navigation rail width
  static double get navigationRailWidth => 72.0;

  /// Desktop-specific breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  /// Check if screen size is mobile
  static bool isMobileSize(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if screen size is tablet
  static bool isTabletSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if screen size is desktop
  static bool isDesktopSize(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobileSize(context)) {
      return const EdgeInsets.all(16);
    } else if (isTabletSize(context)) {
      return const EdgeInsets.all(24);
    } else {
      return const EdgeInsets.all(32);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isMobileSize(context)) {
      return const EdgeInsets.all(8);
    } else if (isTabletSize(context)) {
      return const EdgeInsets.all(12);
    } else {
      return const EdgeInsets.all(16);
    }
  }

  /// Get responsive grid columns count
  static int getGridColumns(BuildContext context) {
    if (isMobileSize(context)) {
      return 2; // عمودين للهواتف
    } else if (isTabletSize(context)) {
      return 3; // ثلاثة أعمدة للأجهزة اللوحية
    } else {
      return 5; // خمسة أعمدة لسطح المكتب
    }
  }

  /// Get responsive card height
  static double getCardHeight(BuildContext context) {
    if (isMobileSize(context)) {
      return 120; // ارتفاع أكبر للهواتف
    } else if (isTabletSize(context)) {
      return 100;
    } else {
      return 90;
    }
  }

  /// Get responsive font size for titles
  static double getTitleFontSize(BuildContext context) {
    if (isMobileSize(context)) {
      return 20;
    } else if (isTabletSize(context)) {
      return 24;
    } else {
      return 28;
    }
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context) {
    if (isMobileSize(context)) {
      return 32;
    } else if (isTabletSize(context)) {
      return 28;
    } else {
      return 24;
    }
  }

  /// Get responsive button padding
  static EdgeInsets getButtonPadding(BuildContext context) {
    if (isMobileSize(context)) {
      return const EdgeInsets.symmetric(horizontal: 20, vertical: 16);
    } else if (isTabletSize(context)) {
      return const EdgeInsets.symmetric(horizontal: 18, vertical: 14);
    } else {
      return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
    }
  }

  /// Get responsive minimum button size
  static Size getMinButtonSize(BuildContext context) {
    if (isMobileSize(context)) {
      return const Size(120, 48); // أزرار أكبر للهواتف
    } else if (isTabletSize(context)) {
      return const Size(110, 44);
    } else {
      return const Size(100, 40);
    }
  }
}

/// Custom scroll behavior for web platforms (especially Chrome)
class WebScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.trackpad,
      };

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    if (kIsWeb) {
      // Use custom physics for web to fix Chrome scrolling issues
      return const WebScrollPhysics();
    }
    return super.getScrollPhysics(context);
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (kIsWeb) {
      // Custom scrollbar for web
      return RawScrollbar(
        controller: details.controller,
        thumbVisibility: true,
        trackVisibility: true,
        thickness: 12,
        radius: const Radius.circular(6),
        thumbColor: Theme.of(context).primaryColor.withValues(alpha: 0.6),
        trackColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        child: child,
      );
    }
    return super.buildScrollbar(context, child, details);
  }
}

/// Custom scroll behavior for desktop platforms
class DesktopScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.trackpad,
      };

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const BouncingScrollPhysics();
  }
}

/// Custom scroll physics for web to fix Chrome scrolling issues
class WebScrollPhysics extends ScrollPhysics {
  const WebScrollPhysics({super.parent});

  @override
  WebScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return WebScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  double get minFlingVelocity => 50.0;

  @override
  double get maxFlingVelocity => 8000.0;

  @override
  double get dragStartDistanceMotionThreshold => 3.5;

  @override
  SpringDescription get spring => const SpringDescription(
        mass: 0.5,
        stiffness: 100.0,
        damping: 0.8,
      );

  @override
  Tolerance get tolerance {
    final devicePixelRatio = WidgetsBinding.instance.platformDispatcher.views.first.devicePixelRatio;
    return Tolerance(
      velocity: 1.0 / (0.050 * devicePixelRatio),
      distance: 1.0 / devicePixelRatio,
    );
  }
}
