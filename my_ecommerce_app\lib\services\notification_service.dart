import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationService extends GetxController {
  static NotificationService get instance => Get.find();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // قائمة التنبيهات
  final RxList<NotificationModel> notifications = <NotificationModel>[].obs;
  final RxInt unreadCount = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeNotifications();
    _loadNotifications();
  }

  // تهيئة نظام التنبيهات
  Future<void> _initializeNotifications() async {
    // طلب الإذن للتنبيهات
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('تم منح إذن التنبيهات');

      // تهيئة التنبيهات المحلية
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings();

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _localNotifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // الاستماع للتنبيهات
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // الحصول على FCM Token
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _saveTokenToDatabase(token);
      }
    }
  }

  // حفظ FCM Token في قاعدة البيانات
  Future<void> _saveTokenToDatabase(String token) async {
    try {
      // يمكن ربطه بالمستخدم الحالي
      await _firestore.collection('fcm_tokens').doc(token).set({
        'token': token,
        'createdAt': FieldValue.serverTimestamp(),
        'platform': GetPlatform.isAndroid
            ? 'android'
            : GetPlatform.isIOS
                ? 'ios'
                : 'web',
      });
    } catch (e) {
      print('خطأ في حفظ FCM Token: $e');
    }
  }

  // التعامل مع التنبيهات في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    _showLocalNotification(
      title: message.notification?.title ?? 'تنبيه جديد',
      body: message.notification?.body ?? '',
      data: message.data,
    );

    _saveNotificationToLocal(message);
  }

  // التعامل مع النقر على التنبيه
  void _handleNotificationTap(RemoteMessage message) {
    _navigateToNotificationScreen(message.data);
  }

  // عرض تنبيه محلي
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'eyewear_store_channel',
      'متجر النظارات',
      channelDescription: 'تنبيهات متجر النظارات',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: false,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      platformChannelSpecifics,
      payload: data?.toString(),
    );
  }

  // حفظ التنبيه محلياً
  void _saveNotificationToLocal(RemoteMessage message) {
    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: message.notification?.title ?? 'تنبيه جديد',
      body: message.notification?.body ?? '',
      data: message.data,
      timestamp: DateTime.now(),
      isRead: false,
    );

    notifications.insert(0, notification);
    unreadCount.value++;

    // حفظ في قاعدة البيانات المحلية أو Firebase
    _saveNotificationToFirestore(notification);
  }

  // حفظ التنبيه في Firestore
  Future<void> _saveNotificationToFirestore(
      NotificationModel notification) async {
    try {
      await _firestore
          .collection('notifications')
          .doc(notification.id)
          .set(notification.toMap());
    } catch (e) {
      print('خطأ في حفظ التنبيه: $e');
    }
  }

  // تحديث التنبيهات (دالة عامة)
  Future<void> refreshNotifications() async {
    await _loadNotifications();
  }

  // تحميل التنبيهات من قاعدة البيانات
  Future<void> _loadNotifications() async {
    try {
      final querySnapshot = await _firestore
          .collection('notifications')
          .orderBy('timestamp', descending: true)
          .limit(50)
          .get();

      final loadedNotifications = querySnapshot.docs
          .map((doc) => NotificationModel.fromMap(doc.data()))
          .toList();

      notifications.assignAll(loadedNotifications);
      unreadCount.value = loadedNotifications.where((n) => !n.isRead).length;
    } catch (e) {
      print('خطأ في تحميل التنبيهات: $e');
    }
  }

  // تحديد التنبيه كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      final index = notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !notifications[index].isRead) {
        notifications[index] = notifications[index].copyWith(isRead: true);
        unreadCount.value--;

        // تحديث في قاعدة البيانات
        await _firestore
            .collection('notifications')
            .doc(notificationId)
            .update({'isRead': true});
      }
    } catch (e) {
      print('خطأ في تحديث التنبيه: $e');
    }
  }

  // تحديد جميع التنبيهات كمقروءة
  Future<void> markAllAsRead() async {
    try {
      final batch = _firestore.batch();

      for (var notification in notifications.where((n) => !n.isRead)) {
        batch.update(
          _firestore.collection('notifications').doc(notification.id),
          {'isRead': true},
        );
      }

      await batch.commit();

      // تحديث محلياً
      for (int i = 0; i < notifications.length; i++) {
        if (!notifications[i].isRead) {
          notifications[i] = notifications[i].copyWith(isRead: true);
        }
      }

      unreadCount.value = 0;
    } catch (e) {
      print('خطأ في تحديث التنبيهات: $e');
    }
  }

  // إرسال تنبيه للمستخدمين
  Future<void> sendNotificationToUsers({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    List<String>? userIds,
    String? topic,
  }) async {
    try {
      // يمكن استخدام Firebase Functions لإرسال التنبيهات
      // أو استخدام Firebase Admin SDK

      final notificationData = {
        'title': title,
        'body': body,
        'data': data ?? {},
        'timestamp': FieldValue.serverTimestamp(),
        'userIds': userIds,
        'topic': topic,
      };

      await _firestore
          .collection('pending_notifications')
          .add(notificationData);

      print('تم إرسال التنبيه بنجاح');
    } catch (e) {
      print('خطأ في إرسال التنبيه: $e');
    }
  }

  // التنقل عند النقر على التنبيه
  void _navigateToNotificationScreen(Map<String, dynamic> data) {
    final type = data['type'];
    final id = data['id'];

    switch (type) {
      case 'order':
        Get.toNamed('/order-details', arguments: id);
        break;
      case 'product':
        Get.toNamed('/product-details', arguments: id);
        break;
      case 'offer':
        Get.toNamed('/offers');
        break;
      default:
        Get.toNamed('/notifications');
    }
  }

  // التعامل مع النقر على التنبيه المحلي
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      // تحليل البيانات والتنقل
      print('تم النقر على التنبيه: ${response.payload}');
    }
  }

  // حذف تنبيه
  Future<void> deleteNotification(String notificationId) async {
    try {
      notifications.removeWhere((n) => n.id == notificationId);
      await _firestore.collection('notifications').doc(notificationId).delete();
    } catch (e) {
      print('خطأ في حذف التنبيه: $e');
    }
  }

  // حذف جميع التنبيهات
  Future<void> clearAllNotifications() async {
    try {
      final batch = _firestore.batch();

      for (var notification in notifications) {
        batch.delete(
            _firestore.collection('notifications').doc(notification.id));
      }

      await batch.commit();
      notifications.clear();
      unreadCount.value = 0;
    } catch (e) {
      print('خطأ في حذف التنبيهات: $e');
    }
  }
}

// نموذج التنبيه
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final bool isRead;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.data,
    required this.timestamp,
    required this.isRead,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'data': data,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
    };
  }

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      timestamp: (map['timestamp'] as Timestamp).toDate(),
      isRead: map['isRead'] ?? false,
    );
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    bool? isRead,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
    );
  }
}
