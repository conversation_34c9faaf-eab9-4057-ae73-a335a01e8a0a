import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/screens/admin/admin_dashboard.dart';
import 'lib/controllers/admin_controller.dart';
import 'lib/services/admin_service.dart';
import 'lib/config/desktop_config.dart';

void main() {
  runApp(const TestResponsiveAdminApp());
}

class TestResponsiveAdminApp extends StatelessWidget {
  const TestResponsiveAdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Test Responsive Admin Dashboard',
      theme: DesktopConfig.adjustThemeForDesktop(
        ThemeData(
          primarySwatch: Colors.teal,
          useMaterial3: true,
          fontFamily: 'Arial',
          textTheme: const TextTheme(
            bodyLarge: TextStyle(fontSize: 16),
            bodyMedium: TextStyle(fontSize: 14),
          ),
        ),
      ),
      scrollBehavior: DesktopConfig.scrollBehavior,
      home: const TestAdminDashboard(),
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),
    );
  }
}

class TestAdminDashboard extends StatelessWidget {
  const TestAdminDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize controllers for testing
    Get.put(AdminService());
    Get.put(AdminController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار لوحة تحكم الإدارة المتجاوبة'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: const AdminDashboard(),
    );
  }
}
