enum CommissionStatus {
  pending,     // معلقة - بانتظار تأكيد الطلب
  approved,    // معتمدة - تم تأكيد الطلب
  processing,  // قيد المعالجة - تم شحن الطلب
  earned,      // مستحقة - وصل الطلب للعميل
  paid,        // مدفوعة - تم دفع العمولة للمسوق
  cancelled,   // ملغية - تم إلغاء الطلب
  refunded     // مسترجعة - تم استرجاع الطلب
}

class Commission {
  final String id;
  final String affiliateId; // معرف المسوق
  final String customerId; // معرف العميل
  final String orderId; // معرف الطلب
  final String productId; // معرف المنتج
  final String trackingCode; // كود التتبع من الرابط
  final double orderAmount; // قيمة الطلب
  final double commissionRate; // نسبة العمولة (0.10 = 10%)
  final double commissionAmount; // مبلغ العمولة
  final CommissionStatus status;
  final DateTime createdAt;
  final DateTime? approvedAt; // تاريخ اعتماد الطلب
  final DateTime? processingAt; // تاريخ شحن الطلب
  final DateTime? earnedAt; // تاريخ وصول الطلب
  final DateTime? paidAt; // تاريخ دفع العمولة
  final String? paymentMethod; // طريقة الدفع
  final String? paymentReference; // مرجع الدفع
  final String? notes; // ملاحظات إضافية

  Commission({
    required this.id,
    required this.affiliateId,
    required this.customerId,
    required this.orderId,
    required this.productId,
    required this.trackingCode,
    required this.orderAmount,
    required this.commissionRate,
    required this.commissionAmount,
    this.status = CommissionStatus.pending,
    required this.createdAt,
    this.approvedAt,
    this.processingAt,
    this.earnedAt,
    this.paidAt,
    this.paymentMethod,
    this.paymentReference,
    this.notes,
  });

  bool get isPending => status == CommissionStatus.pending;
  bool get isApproved => status == CommissionStatus.approved;
  bool get isProcessing => status == CommissionStatus.processing;
  bool get isEarned => status == CommissionStatus.earned;
  bool get isPaid => status == CommissionStatus.paid;
  bool get isCancelled => status == CommissionStatus.cancelled;
  bool get isRefunded => status == CommissionStatus.refunded;

  String get formattedCommissionAmount => "\$${commissionAmount.toStringAsFixed(2)}";
  String get formattedOrderAmount => "\$${orderAmount.toStringAsFixed(2)}";
  String get commissionPercentage => "${(commissionRate * 100).toStringAsFixed(0)}%";

  // حساب العمولة
  static double calculateCommission(double orderAmount, double rate) {
    return orderAmount * rate;
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'affiliateId': affiliateId,
      'customerId': customerId,
      'orderId': orderId,
      'productId': productId,
      'trackingCode': trackingCode,
      'orderAmount': orderAmount,
      'commissionRate': commissionRate,
      'commissionAmount': commissionAmount,
      'status': status.toString(),
      'createdAt': createdAt.toIso8601String(),
      'approvedAt': approvedAt?.toIso8601String(),
      'processingAt': processingAt?.toIso8601String(),
      'earnedAt': earnedAt?.toIso8601String(),
      'paidAt': paidAt?.toIso8601String(),
      'paymentMethod': paymentMethod,
      'paymentReference': paymentReference,
      'notes': notes,
    };
  }

  // إنشاء من JSON
  factory Commission.fromJson(Map<String, dynamic> json) {
    return Commission(
      id: json['id'] ?? '',
      affiliateId: json['affiliateId'] ?? '',
      customerId: json['customerId'] ?? '',
      orderId: json['orderId'] ?? '',
      productId: json['productId'] ?? '',
      trackingCode: json['trackingCode'] ?? '',
      orderAmount: (json['orderAmount'] ?? 0.0).toDouble(),
      commissionRate: (json['commissionRate'] ?? 0.0).toDouble(),
      commissionAmount: (json['commissionAmount'] ?? 0.0).toDouble(),
      status: CommissionStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => CommissionStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      approvedAt: json['approvedAt'] != null
          ? DateTime.parse(json['approvedAt'])
          : null,
      processingAt: json['processingAt'] != null
          ? DateTime.parse(json['processingAt'])
          : null,
      earnedAt: json['earnedAt'] != null
          ? DateTime.parse(json['earnedAt'])
          : null,
      paidAt: json['paidAt'] != null
          ? DateTime.parse(json['paidAt'])
          : null,
      paymentMethod: json['paymentMethod'],
      paymentReference: json['paymentReference'],
      notes: json['notes'],
    );
  }

  // نسخ مع تعديل
  Commission copyWith({
    CommissionStatus? status,
    DateTime? approvedAt,
    DateTime? processingAt,
    DateTime? earnedAt,
    DateTime? paidAt,
    String? paymentMethod,
    String? paymentReference,
    String? notes,
  }) {
    return Commission(
      id: id,
      affiliateId: affiliateId,
      customerId: customerId,
      orderId: orderId,
      productId: productId,
      trackingCode: trackingCode,
      orderAmount: orderAmount,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      status: status ?? this.status,
      createdAt: createdAt,
      approvedAt: approvedAt ?? this.approvedAt,
      processingAt: processingAt ?? this.processingAt,
      earnedAt: earnedAt ?? this.earnedAt,
      paidAt: paidAt ?? this.paidAt,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      notes: notes ?? this.notes,
    );
  }
}

// بيانات تجريبية للعمولات
final List<Commission> demoCommissions = [
  Commission(
    id: 'comm_001',
    affiliateId: 'aff_001',
    customerId: 'cust_001',
    orderId: 'order_001',
    productId: '1',
    trackingCode: 'TRK001',
    orderAmount: 299.99,
    commissionRate: 0.10,
    commissionAmount: 29.99,
    status: CommissionStatus.earned,
    createdAt: DateTime.now().subtract(const Duration(days: 5)),
    approvedAt: DateTime.now().subtract(const Duration(days: 4)),
    processingAt: DateTime.now().subtract(const Duration(days: 3)),
    earnedAt: DateTime.now().subtract(const Duration(days: 1)),
  ),
  Commission(
    id: 'comm_002',
    affiliateId: 'aff_001',
    customerId: 'cust_002',
    orderId: 'order_002',
    productId: '2',
    trackingCode: 'TRK002',
    orderAmount: 199.99,
    commissionRate: 0.10,
    commissionAmount: 19.99,
    status: CommissionStatus.processing,
    createdAt: DateTime.now().subtract(const Duration(days: 2)),
    approvedAt: DateTime.now().subtract(const Duration(days: 1)),
    processingAt: DateTime.now(),
  ),
  Commission(
    id: 'comm_003',
    affiliateId: 'aff_001',
    customerId: 'cust_003',
    orderId: 'order_003',
    productId: '3',
    trackingCode: 'TRK003',
    orderAmount: 159.99,
    commissionRate: 0.10,
    commissionAmount: 15.99,
    status: CommissionStatus.paid,
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    approvedAt: DateTime.now().subtract(const Duration(days: 28)),
    processingAt: DateTime.now().subtract(const Duration(days: 26)),
    earnedAt: DateTime.now().subtract(const Duration(days: 20)),
    paidAt: DateTime.now().subtract(const Duration(days: 10)),
    paymentMethod: 'Bank Transfer',
    paymentReference: 'PAY123456',
  ),
];
