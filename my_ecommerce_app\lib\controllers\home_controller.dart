import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../services/product_database_service.dart';

class HomeController extends GetxController {
  final ProductDatabaseService _productService = ProductDatabaseService();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isLoadingFeatured = false.obs;
  final RxBool isLoadingBestSellers = false.obs;
  final RxBool isLoadingRecommended = false.obs;

  // قوائم المنتجات
  final RxList<Product> allProducts = <Product>[].obs;
  final RxList<Product> featuredProducts = <Product>[].obs;
  final RxList<Product> bestSellerProducts = <Product>[].obs;
  final RxList<Product> recommendedProducts = <Product>[].obs;

  // الفئات
  final RxList<Category> categories = <Category>[].obs;

  // رسائل الخطأ
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // التحقق من وجود منتجات في قاعدة البيانات
      final hasProducts = await _productService.hasProducts();

      // إذا لم توجد منتجات، أضف البيانات التجريبية
      if (!hasProducts) {
        debugPrint(
            'لا توجد منتجات في قاعدة البيانات، جاري إضافة البيانات التجريبية...');
        await _productService.addDemoProducts();
      }

      // تحميل البيانات بشكل متوازي
      await Future.wait([
        loadAllProducts(),
        loadFeaturedProducts(),
        loadBestSellerProducts(),
        loadRecommendedProducts(),
        loadCategories(),
      ]);
    } catch (e) {
      errorMessage.value = 'حدث خطأ في تحميل البيانات: $e';
      print('خطأ في تحميل البيانات الأولية: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل جميع المنتجات
  Future<void> loadAllProducts() async {
    try {
      final products = await _productService.getAllProducts();
      if (products.isNotEmpty) {
        allProducts.assignAll(products);
      } else {
        // في حالة عدم وجود منتجات في قاعدة البيانات، استخدم البيانات التجريبية
        allProducts.assignAll(Product.demoProducts);
      }
    } catch (e) {
      print('خطأ في تحميل جميع المنتجات: $e');
      // في حالة الخطأ، استخدم البيانات التجريبية
      allProducts.assignAll(Product.demoProducts);
    }
  }

  /// تحميل المنتجات المميزة
  Future<void> loadFeaturedProducts() async {
    if (isLoadingFeatured.value) return; // تجنب التحميل المتكرر

    isLoadingFeatured.value = true;
    try {
      print('🔄 بدء تحميل المنتجات المميزة...');

      final products = await _productService.getFeaturedProducts();

      if (products.isNotEmpty) {
        featuredProducts.assignAll(products.take(8).toList());
        print('✅ تم تحميل ${featuredProducts.length} منتج مميز');
      } else {
        print('⚠️ لا توجد منتجات مميزة، استخدام البيانات التجريبية');
        featuredProducts.assignAll(Product.demoProducts.take(6).toList());
      }
    } catch (e) {
      print('❌ خطأ في تحميل المنتجات المميزة: $e');
      // في حالة الخطأ، استخدم البيانات التجريبية
      featuredProducts.assignAll(Product.demoProducts.take(6).toList());
    } finally {
      isLoadingFeatured.value = false;
    }
  }

  /// تحميل المنتجات الأكثر مبيعاً
  Future<void> loadBestSellerProducts() async {
    isLoadingBestSellers.value = true;
    try {
      // جلب المنتجات مرتبة حسب عدد المبيعات (يمكن تحسينها لاحقاً)
      final products = allProducts.isNotEmpty
          ? List<Product>.from(allProducts)
          : await _productService.getAllProducts();

      if (products.isNotEmpty) {
        // ترتيب المنتجات حسب التقييم وعدد المراجعات (محاكاة للأكثر مبيعاً)
        products.sort((a, b) {
          final scoreA = a.rating * a.reviewCount;
          final scoreB = b.rating * b.reviewCount;
          return scoreB.compareTo(scoreA);
        });

        bestSellerProducts.assignAll(products.take(6).toList());
      } else {
        bestSellerProducts.assignAll(
            Product.demoProducts.where((p) => p.isPopular).take(6).toList());
      }
    } catch (e) {
      print('خطأ في تحميل المنتجات الأكثر مبيعاً: $e');
      // في حالة الخطأ، استخدم البيانات التجريبية
      bestSellerProducts.assignAll(
          Product.demoProducts.where((p) => p.isPopular).take(6).toList());
    } finally {
      isLoadingBestSellers.value = false;
    }
  }

  /// تحميل المنتجات الموصى بها
  Future<void> loadRecommendedProducts() async {
    isLoadingRecommended.value = true;
    try {
      final products = allProducts.isNotEmpty
          ? List<Product>.from(allProducts)
          : await _productService.getAllProducts();

      if (products.isNotEmpty) {
        // ترتيب المنتجات حسب التقييم
        products.sort((a, b) => b.rating.compareTo(a.rating));
        recommendedProducts.assignAll(products.take(8).toList());
      } else {
        recommendedProducts.assignAll(Product.demoProducts
            .where((p) => p.isRecommended)
            .take(8)
            .toList());
      }
    } catch (e) {
      print('خطأ في تحميل المنتجات الموصى بها: $e');
      // في حالة الخطأ، استخدم البيانات التجريبية
      recommendedProducts.assignAll(
          Product.demoProducts.where((p) => p.isRecommended).take(8).toList());
    } finally {
      isLoadingRecommended.value = false;
    }
  }

  /// تحميل الفئات
  Future<void> loadCategories() async {
    try {
      // حالياً نستخدم الفئات التجريبية
      // يمكن إضافة خدمة قاعدة بيانات للفئات لاحقاً
      categories.assignAll(Category.demoCategories);
    } catch (e) {
      print('خطأ في تحميل الفئات: $e');
      categories.assignAll(Category.demoCategories);
    }
  }

  /// إعادة تحميل البيانات
  Future<void> refreshData() async {
    await loadInitialData();
  }

  /// البحث في المنتجات
  Future<List<Product>> searchProducts(String query) async {
    if (query.isEmpty) return [];

    try {
      return await _productService.searchProducts(query);
    } catch (e) {
      print('خطأ في البحث: $e');
      // البحث في البيانات المحلية كبديل
      return allProducts
          .where((product) =>
              product.name.toLowerCase().contains(query.toLowerCase()) ||
              product.description.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
  }

  /// جلب المنتجات بالفئة
  Future<List<Product>> getProductsByCategory(String category) async {
    try {
      return await _productService.getProductsByCategory(category);
    } catch (e) {
      print('خطأ في جلب المنتجات بالفئة: $e');
      return [];
    }
  }

  /// إضافة منتج للمفضلة (يمكن تطويرها لاحقاً)
  void toggleFavorite(Product product) {
    // TODO: تنفيذ إضافة/إزالة من المفضلة
    Get.snackbar(
      'المفضلة',
      'تم إضافة ${product.name} للمفضلة',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// إضافة منتج للسلة (يمكن تطويرها لاحقاً)
  void addToCart(Product product) {
    // TODO: تنفيذ إضافة للسلة
    Get.snackbar(
      'السلة',
      'تم إضافة ${product.name} للسلة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.primaryColor,
      colorText: Get.theme.colorScheme.onPrimary,
    );
  }

  /// التحقق من وجود بيانات
  bool get hasData =>
      featuredProducts.isNotEmpty ||
      bestSellerProducts.isNotEmpty ||
      recommendedProducts.isNotEmpty;

  /// التحقق من وجود خطأ
  bool get hasError => errorMessage.value.isNotEmpty;

  // للتوافق مع الكود القديم
  List<Product> get products => allProducts;
  List<Product> get popularProducts => bestSellerProducts;

  /// إعادة تعيين البيانات التجريبية
  Future<void> resetDemoData() async {
    try {
      isLoading.value = true;

      // مسح البيانات الحالية
      await _productService.clearAllProducts();

      // إضافة البيانات التجريبية الجديدة
      await _productService.addDemoProducts();

      // إعادة تحميل البيانات
      await loadInitialData();

      Get.snackbar(
        'نجح',
        'تم إعادة تعيين البيانات التجريبية بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في إعادة تعيين البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
