import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/admin_controller.dart';
import '../../services/auth_service.dart';
import '../../widgets/user_role_widgets.dart';
import '../../models/order.dart';
import '../../models/affiliate_request.dart';
import '../../config/desktop_config.dart';
import 'admin_users_screen.dart';
import 'admin_products_screen.dart';

class AdminDashboard extends StatelessWidget {
  const AdminDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    final adminController = Get.find<AdminController>();
    final isMobile = DesktopConfig.isMobileSize(context);

    return Scaffold(
      appBar: const UserRoleAppBar(title: "لوحة تحكم الإدارة"),
      drawer: isMobile ? _buildMobileDrawer(adminController) : null,
      body: isMobile
          ? _buildMobileLayout(adminController)
          : _buildDesktopLayout(adminController),
    );
  }

  Widget _buildDesktopLayout(AdminController adminController) {
    return Row(
      children: [
        // Sidebar Navigation
        Container(
          width: 250,
          color: Colors.grey[100],
          child: _buildSidebar(adminController),
        ),

        // Main Content
        Expanded(
          child: Obx(() => _buildMainContent(adminController)),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(AdminController adminController) {
    return Obx(() => _buildMainContent(adminController));
  }

  Widget _buildMobileDrawer(AdminController controller) {
    return Drawer(
      child: _buildSidebar(controller),
    );
  }

  Widget _buildSidebar(AdminController controller) {
    return Column(
      children: [
        const SizedBox(height: 20),

        // Admin Profile
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const CircleAvatar(
                radius: 30,
                backgroundColor: Colors.red,
                child: Icon(Icons.admin_panel_settings,
                    color: Colors.white, size: 30),
              ),
              const SizedBox(height: 8),
              const Text(
                'مدير النظام',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              Text(
                '<EMAIL>',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
        ),

        const Divider(),

        // Navigation Items
        Expanded(
          child: ListView(
            children: [
              _buildNavItem(
                controller,
                'dashboard',
                Icons.dashboard,
                'لوحة التحكم',
                isSelected: controller.selectedTab.value == 'dashboard',
              ),
              _buildNavItem(
                controller,
                'users',
                Icons.people,
                'إدارة المستخدمين',
                isSelected: controller.selectedTab.value == 'users',
              ),
              _buildNavItem(
                controller,
                'products',
                Icons.inventory,
                'إدارة المنتجات',
                isSelected: controller.selectedTab.value == 'products',
              ),
              _buildNavItem(
                controller,
                'orders',
                Icons.shopping_cart,
                'إدارة الطلبات',
                isSelected: controller.selectedTab.value == 'orders',
              ),
              _buildNavItem(
                controller,
                'affiliates',
                Icons.group_add,
                'طلبات المسوقين',
                isSelected: controller.selectedTab.value == 'affiliates',
              ),
              _buildNavItem(
                controller,
                'reports',
                Icons.analytics,
                'التقارير والإحصائيات',
                isSelected: controller.selectedTab.value == 'reports',
              ),
              _buildNavItem(
                controller,
                'settings',
                Icons.settings,
                'إعدادات النظام',
                isSelected: controller.selectedTab.value == 'settings',
              ),
            ],
          ),
        ),

        const Divider(),

        // Logout Button
        ListTile(
          leading: const Icon(Icons.logout, color: Colors.red),
          title:
              const Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
          onTap: () => Get.find<AuthService>().logout(),
        ),

        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildNavItem(
    AdminController controller,
    String tabId,
    IconData icon,
    String title, {
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(8),
        border:
            isSelected ? Border.all(color: Colors.blue.withValues(alpha: 0.3)) : null,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey[600],
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.blue : Colors.grey[800],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        onTap: () => controller.changeTab(tabId),
      ),
    );
  }

  Widget _buildMainContent(AdminController controller) {
    switch (controller.selectedTab.value) {
      case 'dashboard':
        return _buildDashboardContent(controller);
      case 'users':
        return const AdminUsersScreen();
      case 'products':
        return const AdminProductsScreen();
      case 'orders':
        return _buildOrdersManagementContent(controller);
      case 'affiliates':
        return _buildAffiliatesContent(controller);
      case 'reports':
        return _buildReportsContent(controller);
      case 'settings':
        return _buildSettingsContent(controller);
      default:
        return _buildDashboardContent(controller);
    }
  }

  Widget _buildDashboardContent(AdminController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = DesktopConfig.isMobileSize(context);
        final padding = DesktopConfig.getResponsivePadding(context);

        return SingleChildScrollView(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              isMobile
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'لوحة التحكم الرئيسية',
                          style: TextStyle(
                              fontSize: 24, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'آخر تحديث: ${DateTime.now().toString().substring(0, 16)}',
                          style:
                              TextStyle(color: Colors.grey[600], fontSize: 12),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'لوحة التحكم الرئيسية',
                          style: TextStyle(
                              fontSize: 28, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'آخر تحديث: ${DateTime.now().toString().substring(0, 16)}',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),

              const SizedBox(height: 24),

              // Statistics Cards
              _buildStatsCards(controller),

              const SizedBox(height: 32),

              // Charts and Recent Activity
              isMobile
                  ? Column(
                      children: [
                        _buildQuickActions(controller),
                        const SizedBox(height: 24),
                        _buildRecentOrders(controller),
                      ],
                    )
                  : Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Recent Orders
                        Expanded(
                          flex: 2,
                          child: _buildRecentOrders(controller),
                        ),

                        const SizedBox(width: 24),

                        // Quick Actions
                        Expanded(
                          flex: 1,
                          child: _buildQuickActions(controller),
                        ),
                      ],
                    ),

              const SizedBox(height: 32),

              // Recent Activity and Alerts
              isMobile
                  ? Column(
                      children: [
                        _buildRecentActivity(controller),
                        const SizedBox(height: 24),
                        _buildSystemAlerts(controller),
                      ],
                    )
                  : Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: _buildRecentActivity(controller),
                        ),
                        const SizedBox(width: 24),
                        Expanded(
                          child: _buildSystemAlerts(controller),
                        ),
                      ],
                    ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsCards(AdminController controller) {
    final analytics = controller.dashboardAnalytics;

    return Builder(
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = screenWidth < 600;

        final statsData = [
          {
            'title': 'إجمالي المستخدمين',
            'value': '${analytics['totalUsers']}',
            'icon': Icons.people,
            'color': Colors.blue,
            'subtitle': '${analytics['activeUsers']} نشط',
          },
          {
            'title': 'إجمالي المنتجات',
            'value': '${analytics['totalProducts']}',
            'icon': Icons.inventory,
            'color': Colors.green,
            'subtitle': '${analytics['inStockProducts']} متوفر',
          },
          {
            'title': 'إجمالي الطلبات',
            'value': '${analytics['totalOrders']}',
            'icon': Icons.shopping_cart,
            'color': Colors.orange,
            'subtitle': '${analytics['pendingOrders']} قيد المراجعة',
          },
          {
            'title': 'إجمالي المبيعات',
            'value': '${analytics['totalRevenue'].toStringAsFixed(0)} دج',
            'icon': Icons.attach_money,
            'color': Colors.purple,
            'subtitle':
                'هذا الشهر: ${analytics['monthlyRevenue'].toStringAsFixed(0)} دج',
          },
          {
            'title': 'المسوقون',
            'value': '${analytics['totalAffiliates']}',
            'icon': Icons.group,
            'color': Colors.teal,
            'subtitle': '${analytics['pendingAffiliateRequests']} طلب جديد',
          },
          {
            'title': 'مخزون منخفض',
            'value': '${analytics['lowStockProducts']}',
            'icon': Icons.warning,
            'color': Colors.red,
            'subtitle': '${analytics['outOfStockProducts']} نفد المخزون',
          },
          {
            'title': 'طلبات هذا الأسبوع',
            'value': '${analytics['weeklyOrders']}',
            'icon': Icons.trending_up,
            'color': Colors.indigo,
            'subtitle':
                'مبيعات: ${analytics['weeklyRevenue'].toStringAsFixed(0)} دج',
          },
          {
            'title': 'طلبات هذا الشهر',
            'value': '${analytics['monthlyOrders']}',
            'icon': Icons.calendar_month,
            'color': Colors.cyan,
            'subtitle': 'نمو: +12%',
          },
        ];

        if (isMobile) {
          // Mobile layout: Grid with 2 columns
          return GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.1, // نسبة أفضل للهواتف
            ),
            itemCount: statsData.length,
            itemBuilder: (context, index) {
              final stat = statsData[index];
              return _buildStatCard(
                stat['title'] as String,
                stat['value'] as String,
                stat['icon'] as IconData,
                stat['color'] as Color,
                subtitle: stat['subtitle'] as String,
              );
            },
          );
        } else {
          // Desktop layout: 4 cards per row
          return Column(
            children: [
              IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: statsData
                      .take(4)
                      .map(
                        (stat) => Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(right: 16),
                            child: _buildStatCard(
                              stat['title'] as String,
                              stat['value'] as String,
                              stat['icon'] as IconData,
                              stat['color'] as Color,
                              subtitle: stat['subtitle'] as String,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ),
              const SizedBox(height: 16),
              IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: statsData
                      .skip(4)
                      .map(
                        (stat) => Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(right: 16),
                            child: _buildStatCard(
                              stat['title'] as String,
                              stat['value'] as String,
                              stat['icon'] as IconData,
                              stat['color'] as Color,
                              subtitle: stat['subtitle'] as String,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Builder(
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = screenWidth < 600;

        return Card(
          elevation: 2,
          child: Padding(
            padding: EdgeInsets.all(isMobile ? 16 : 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: EdgeInsets.all(isMobile ? 8 : 12),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                      ),
                      child: Icon(
                        icon,
                        color: color,
                        size: isMobile ? 20 : 28,
                      ),
                    ),
                    Icon(
                      Icons.more_vert,
                      color: Colors.grey[400],
                      size: isMobile ? 18 : 24,
                    ),
                  ],
                ),
                SizedBox(height: isMobile ? 12 : 16),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: isMobile ? 20 : 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: isMobile ? 12 : 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: isMobile ? 10 : 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentOrders(AdminController controller) {
    final recentOrders = controller.orders.take(5).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الطلبات الأخيرة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () => controller.changeTab('orders'),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...recentOrders
                .map((order) => ListTile(
                      leading: CircleAvatar(
                        backgroundColor: _getOrderStatusColor(order.status),
                        child: Icon(
                          _getOrderStatusIcon(order.status),
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      title: Text('طلب #${order.id.substring(0, 8)}'),
                      subtitle: Text(order.shippingAddress.fullName),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${order.totalAmount.toStringAsFixed(0)} دج',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            _getOrderStatusText(order.status),
                            style: TextStyle(
                              color: _getOrderStatusColor(order.status),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ))

          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(AdminController controller) {
    return Builder(
      builder: (context) {
        final isMobile = DesktopConfig.isMobileSize(context);
        final padding = DesktopConfig.getResponsivePadding(context);

        final quickActions = [
          {
            'title': 'إضافة مستخدم جديد',
            'icon': Icons.person_add,
            'color': Colors.blue,
            'onTap': () => _showAddUserDialog(controller),
          },
          {
            'title': 'إضافة منتج جديد',
            'icon': Icons.add_box,
            'color': Colors.green,
            'onTap': () => _showAddProductDialog(controller),
          },
          {
            'title': 'عرض التقارير',
            'icon': Icons.analytics,
            'color': Colors.orange,
            'onTap': () => controller.changeTab('reports'),
          },
          {
            'title': 'إعدادات النظام',
            'icon': Icons.settings,
            'color': Colors.purple,
            'onTap': () => controller.changeTab('settings'),
          },
        ];

        return Card(
          child: Padding(
            padding: padding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجراءات سريعة',
                  style: TextStyle(
                    fontSize: isMobile ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[700],
                  ),
                ),
                SizedBox(height: isMobile ? 16 : 12),
                if (isMobile) ...[
                  // تخطيط شبكي للهواتف
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1.3,
                    ),
                    itemCount: quickActions.length,
                    itemBuilder: (context, index) {
                      final action = quickActions[index];
                      return _buildQuickActionCard(
                        action['title'] as String,
                        action['icon'] as IconData,
                        action['color'] as Color,
                        action['onTap'] as VoidCallback,
                      );
                    },
                  ),
                ] else ...[
                  // تخطيط عمودي للشاشات الكبيرة
                  ...quickActions.map((action) => Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: _buildQuickActionButton(
                          action['title'] as String,
                          action['icon'] as IconData,
                          action['color'] as Color,
                          action['onTap'] as VoidCallback,
                        ),
                      )),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Builder(
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = screenWidth < 600;

        return InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: EdgeInsets.all(isMobile ? 16 : 12),
            decoration: BoxDecoration(
              border: Border.all(color: color.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isMobile ? 12 : 8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: isMobile ? 24 : 20,
                  ),
                ),
                SizedBox(width: isMobile ? 16 : 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isMobile ? 16 : 14,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: isMobile ? 20 : 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          border: Border.all(color: color.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 28,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 13,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(AdminController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'النشاط الأخير',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'تم إنشاء طلب جديد #12345',
              'منذ 5 دقائق',
              Icons.shopping_cart,
              Colors.green,
            ),
            _buildActivityItem(
              'تم تسجيل مستخدم جديد',
              'منذ 15 دقيقة',
              Icons.person_add,
              Colors.blue,
            ),
            _buildActivityItem(
              'تم تحديث منتج',
              'منذ 30 دقيقة',
              Icons.edit,
              Colors.orange,
            ),
            _buildActivityItem(
              'تم الموافقة على طلب مسوق',
              'منذ ساعة',
              Icons.group_add,
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  time,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemAlerts(AdminController controller) {
    final analytics = controller.dashboardAnalytics;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تنبيهات النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (analytics['lowStockProducts'] > 0)
              _buildAlertItem(
                'مخزون منخفض',
                '${analytics['lowStockProducts']} منتج يحتاج إعادة تموين',
                Icons.warning,
                Colors.orange,
                () => controller.changeTab('products'),
              ),
            if (analytics['outOfStockProducts'] > 0)
              _buildAlertItem(
                'نفد المخزون',
                '${analytics['outOfStockProducts']} منتج نفد من المخزون',
                Icons.error,
                Colors.red,
                () => controller.changeTab('products'),
              ),
            if (analytics['pendingOrders'] > 0)
              _buildAlertItem(
                'طلبات معلقة',
                '${analytics['pendingOrders']} طلب يحتاج مراجعة',
                Icons.pending_actions,
                Colors.blue,
                () => controller.changeTab('orders'),
              ),
            if (analytics['pendingAffiliateRequests'] > 0)
              _buildAlertItem(
                'طلبات مسوقين',
                '${analytics['pendingAffiliateRequests']} طلب جديد للمراجعة',
                Icons.group_add,
                Colors.purple,
                () => controller.changeTab('affiliates'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          border: Border.all(color: color.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  Widget _buildAffiliatesContent(AdminController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'طلبات المسوقين',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: controller.affiliateRequests.map((request) {
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getRequestStatusColor(request.status),
                      child: Icon(
                        _getRequestStatusIcon(request.status),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(request.userFullName),
                    subtitle: Text(request.userEmail),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (request.isPending) ...[
                          IconButton(
                            onPressed: () =>
                                controller.approveAffiliateRequest(request.id),
                            icon: const Icon(Icons.check, color: Colors.green),
                            tooltip: 'موافقة',
                          ),
                          IconButton(
                            onPressed: () =>
                                controller.rejectAffiliateRequest(request.id),
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'رفض',
                          ),
                        ] else
                          Chip(
                            label: Text(request.statusText),
                            backgroundColor:
                                _getRequestStatusColor(request.status)
                                    .withValues(alpha: 0.1),
                          ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for status colors and icons
  Color _getOrderStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.processing:
        return Colors.purple;
      case OrderStatus.shipped:
        return Colors.indigo;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
      case OrderStatus.returned:
        return Colors.grey;
    }
  }

  IconData _getOrderStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Icons.pending;
      case OrderStatus.confirmed:
        return Icons.check_circle;
      case OrderStatus.processing:
        return Icons.settings;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.done_all;
      case OrderStatus.cancelled:
        return Icons.cancel;
      case OrderStatus.returned:
        return Icons.keyboard_return;
    }
  }

  String _getOrderStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'قيد المراجعة';
      case OrderStatus.confirmed:
        return 'مؤكد';
      case OrderStatus.processing:
        return 'قيد التحضير';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.returned:
        return 'مرتجع';
    }
  }

  Color _getRequestStatusColor(AffiliateRequestStatus status) {
    switch (status) {
      case AffiliateRequestStatus.pending:
        return Colors.orange;
      case AffiliateRequestStatus.approved:
        return Colors.green;
      case AffiliateRequestStatus.rejected:
        return Colors.red;
    }
  }

  IconData _getRequestStatusIcon(AffiliateRequestStatus status) {
    switch (status) {
      case AffiliateRequestStatus.pending:
        return Icons.pending;
      case AffiliateRequestStatus.approved:
        return Icons.check;
      case AffiliateRequestStatus.rejected:
        return Icons.close;
    }
  }

  // Dialog methods
  void _showAddUserDialog(AdminController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة مستخدم جديد'),
        content: const Text('سيتم فتح نموذج إضافة مستخدم جديد'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.changeTab('users');
            },
            child: const Text('انتقال لإدارة المستخدمين'),
          ),
        ],
      ),
    );
  }

  void _showAddProductDialog(AdminController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة منتج جديد'),
        content: const Text('سيتم فتح نموذج إضافة منتج جديد'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.changeTab('products');
            },
            child: const Text('انتقال لإدارة المنتجات'),
          ),
        ],
      ),
    );
  }





  Widget _buildSystemSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.settings, color: Colors.blue),
              title: const Text('إعدادات عامة'),
              subtitle: const Text('إدارة إعدادات التطبيق'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showSettingsDialog(),
            ),
            ListTile(
              leading: const Icon(Icons.analytics, color: Colors.green),
              title: const Text('التقارير والإحصائيات'),
              subtitle: const Text('عرض تقارير مفصلة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showReportsDialog(),
            ),
            ListTile(
              leading: const Icon(Icons.backup, color: Colors.orange),
              title: const Text('النسخ الاحتياطي'),
              subtitle: const Text('إدارة النسخ الاحتياطية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showBackupDialog(),
            ),
            ListTile(
              leading: const Icon(Icons.storage, color: Colors.purple),
              title: const Text('اختبار قاعدة البيانات'),
              subtitle: const Text('اختبار ربط خدمة الإدارة بـ Firestore'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => Get.toNamed('/admin-database-test'),
            ),
            ListTile(
              leading: const Icon(Icons.people, color: Colors.orange),
              title: const Text('اختبار قاعدة بيانات المسوقين'),
              subtitle: const Text('اختبار ربط خدمة المسوقين بـ Firestore'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => Get.toNamed('/affiliate-database-test'),
            ),
            ListTile(
              leading: const Icon(Icons.store, color: Colors.green),
              title: const Text('اختبار قاعدة بيانات المتجر'),
              subtitle: const Text('اختبار ربط المنتجات والطلبات بـ Firestore'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => Get.toNamed('/store-database-test'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersManagementContent(AdminController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إدارة الطلبات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  const Text(
                    'الطلبات الأخيرة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 20),
                  ...controller.orders.take(10).map((order) => ListTile(
                        leading: CircleAvatar(
                          backgroundColor: _getOrderStatusColor(order.status),
                          child: Icon(
                            _getOrderStatusIcon(order.status),
                            color: Colors.white,
                          ),
                        ),
                        title: Text('طلب #${order.id.substring(0, 8)}'),
                        subtitle: Text(order.shippingAddress.fullName),
                        trailing:
                            Text('${order.totalAmount.toStringAsFixed(0)} دج'),
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportsContent(AdminController controller) {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التقارير والإحصائيات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 24),
          Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    'تقارير المبيعات',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 20),
                  Text('سيتم إضافة التقارير والرسوم البيانية هنا'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent(AdminController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات النظام',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.settings, color: Colors.blue),
                    title: const Text('إعدادات عامة'),
                    subtitle: const Text('إدارة إعدادات التطبيق'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showSettingsDialog(),
                  ),
                  ListTile(
                    leading:
                        const Icon(Icons.notifications, color: Colors.green),
                    title: const Text('إعدادات الإشعارات'),
                    subtitle: const Text('إدارة الإشعارات والتنبيهات'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showNotificationSettings(),
                  ),
                  ListTile(
                    leading: const Icon(Icons.backup, color: Colors.orange),
                    title: const Text('النسخ الاحتياطي'),
                    subtitle: const Text('إدارة النسخ الاحتياطية'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showBackupDialog(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    Get.snackbar('إعدادات الإشعارات', 'سيتم فتح إعدادات الإشعارات');
  }



  void _showSettingsDialog() {
    Get.snackbar('الإعدادات', 'سيتم فتح صفحة الإعدادات');
  }

  void _showReportsDialog() {
    Get.snackbar('التقارير', 'سيتم فتح صفحة التقارير');
  }

  void _showBackupDialog() {
    Get.snackbar('النسخ الاحتياطي', 'سيتم فتح إعدادات النسخ الاحتياطي');
  }
}
