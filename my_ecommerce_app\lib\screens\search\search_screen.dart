import 'package:flutter/material.dart';
import '../../widgets/product_card.dart';
import '../../models/product.dart';
import '../../widgets/user_role_widgets.dart';
import '../../utils/layout_error_handler.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Product> _searchResults = [];
  final List<Product> _allProducts = Product.demoProducts;
  bool _isSearching = false;

  // فلاتر البحث
  String _selectedCategory = 'الكل';
  String _selectedBrand = 'الكل';
  RangeValues _priceRange = const RangeValues(0, 20000);
  double _minRating = 0;

  final List<String> _categories = [
    'الكل',
    'نظارات شمسية',
    'نظارات طبية',
    'نظارات رياضية',
    'نظارات قراءة',
  ];

  final List<String> _brands = [
    'الكل',
    'Ray-Ban',
    'Oakley',
    'Nike',
    'Foster Grant',
  ];

  @override
  void initState() {
    super.initState();
    _searchResults = _allProducts;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const UserRoleAppBar(title: 'البحث'),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),

          // شريط الفلاتر
          _buildFiltersBar(),

          // النتائج
          Expanded(
            child: _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن النظارات...',
                prefixIcon: const Icon(Icons.search, color: Colors.teal),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              ),
              onChanged: _performSearch,
              onSubmitted: _performSearch,
            ),
          ),
          const SizedBox(width: 12),
          IconButton(
            onPressed: _showFiltersDialog,
            icon: const Icon(Icons.tune, color: Colors.teal),
            style: IconButton.styleFrom(
              backgroundColor: Colors.teal.shade50,
              padding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersBar() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(
              'الفئة: $_selectedCategory', () => _showCategoryFilter()),
          _buildFilterChip(
              'العلامة: $_selectedBrand', () => _showBrandFilter()),
          _buildFilterChip(
              'السعر: ${_priceRange.start.round()}-${_priceRange.end.round()} دج',
              () => _showPriceFilter()),
          _buildFilterChip('التقييم: ${_minRating.round()}+ نجوم',
              () => _showRatingFilter()),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        onSelected: (_) => onTap(),
        backgroundColor: Colors.grey.shade100,
        selectedColor: Colors.teal.shade100,
        labelStyle: const TextStyle(fontSize: 12),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.teal),
            SizedBox(height: 16),
            Text('جاري البحث...'),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'جرب البحث بكلمات مختلفة أو تعديل الفلاتر',
                style: TextStyle(
                  color: Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive grid parameters
        final crossAxisCount = _getCrossAxisCount(constraints.maxWidth);
        final childAspectRatio = _getChildAspectRatio(constraints.maxWidth);

        return GridView.builder(
          padding: const EdgeInsets.all(16),
          physics: const BouncingScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: _searchResults.length,
          itemBuilder: (context, index) {
            return RepaintBoundary(
              child: Container(
                constraints: const BoxConstraints(
                  minHeight: 200,
                  maxHeight: 400,
                ),
                child: ProductCard(product: _searchResults[index]),
              ),
            );
          },
        );
      },
    );
  }

  // Helper method to determine cross axis count based on screen width
  int _getCrossAxisCount(double width) {
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2;
  }

  // Helper method to determine child aspect ratio based on screen width
  double _getChildAspectRatio(double width) {
    if (width > 1200) return 0.8;
    if (width > 800) return 0.75;
    return 0.7;
  }

  void _performSearch(String query) {
    setState(() {
      _isSearching = true;
    });

    // محاكاة تأخير البحث
    Future.delayed(const Duration(milliseconds: 300), () {
      setState(() {
        _searchResults = _filterProducts(query);
        _isSearching = false;
      });
    });
  }

  List<Product> _filterProducts(String query) {
    List<Product> results = _allProducts;

    // فلترة بالنص
    if (query.isNotEmpty) {
      results = results.where((product) {
        return product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()) ||
            product.brand.toLowerCase().contains(query.toLowerCase()) ||
            product.category.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }

    // فلترة بالفئة
    if (_selectedCategory != 'الكل') {
      results = results
          .where((product) => product.category == _selectedCategory)
          .toList();
    }

    // فلترة بالعلامة التجارية
    if (_selectedBrand != 'الكل') {
      results =
          results.where((product) => product.brand == _selectedBrand).toList();
    }

    // فلترة بالسعر
    results = results.where((product) {
      return product.price >= _priceRange.start &&
          product.price <= _priceRange.end;
    }).toList();

    // فلترة بالتقييم
    results = results.where((product) => product.rating >= _minRating).toList();

    return results;
  }

  void _showFiltersDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) =>
            _buildFiltersSheet(scrollController),
      ),
    );
  }

  Widget _buildFiltersSheet(ScrollController scrollController) {
    return StatefulBuilder(
      builder: (context, setModalState) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // عنوان
            const Text(
              'فلترة النتائج',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            Flexible(
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                  // فلتر الفئة
                  _buildFilterSection(
                    'الفئة',
                    DropdownButton<String>(
                      value: _selectedCategory,
                      isExpanded: true,
                      items: _categories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setModalState(() => _selectedCategory = value!);
                      },
                    ),
                  ),

                  // فلتر العلامة التجارية
                  _buildFilterSection(
                    'العلامة التجارية',
                    DropdownButton<String>(
                      value: _selectedBrand,
                      isExpanded: true,
                      items: _brands.map((brand) {
                        return DropdownMenuItem(
                          value: brand,
                          child: Text(brand),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setModalState(() => _selectedBrand = value!);
                      },
                    ),
                  ),

                  // فلتر السعر
                  _buildFilterSection(
                    'نطاق السعر (${_priceRange.start.round()} - ${_priceRange.end.round()} دج)',
                    RangeSlider(
                      values: _priceRange,
                      min: 0,
                      max: 20000,
                      divisions: 20,
                      labels: RangeLabels(
                        '${_priceRange.start.round()} دج',
                        '${_priceRange.end.round()} دج',
                      ),
                      onChanged: (values) {
                        setModalState(() => _priceRange = values);
                      },
                    ),
                  ),

                  // فلتر التقييم
                  _buildFilterSection(
                    'التقييم الأدنى (${_minRating.round()} نجوم)',
                    Slider(
                      value: _minRating,
                      min: 0,
                      max: 5,
                      divisions: 5,
                      label: '${_minRating.round()} نجوم',
                      onChanged: (value) {
                        setModalState(() => _minRating = value);
                      },
                    ),
                  ),
                ],
              ),
            ),
            ),

            // أزرار الإجراءات
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setModalState(() {
                        _selectedCategory = 'الكل';
                        _selectedBrand = 'الكل';
                        _priceRange = const RangeValues(0, 20000);
                        _minRating = 0;
                      });
                    },
                    child: const Text('إعادة تعيين'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _performSearch(_searchController.text);
                      });
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('تطبيق الفلاتر'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection(String title, Widget child) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          child,
        ],
      ),
    );
  }

  void _showCategoryFilter() => _showFiltersDialog();
  void _showBrandFilter() => _showFiltersDialog();
  void _showPriceFilter() => _showFiltersDialog();
  void _showRatingFilter() => _showFiltersDialog();
}
