import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../firebase_options.dart';

/// خدمة تشخيص مشاكل Firebase
class FirebaseDiagnosticService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// تشخيص شامل لإعدادات Firebase
  static Future<Map<String, dynamic>> runDiagnostics() async {
    final results = <String, dynamic>{};
    
    print('🔍 بدء التشخيص الشامل لـ Firebase...\n');

    // 1. فحص تهيئة Firebase
    results['firebase_initialized'] = await _checkFirebaseInitialization();
    
    // 2. فحص إعدادات Firebase
    results['firebase_config'] = await _checkFirebaseConfig();
    
    // 3. فحص الاتصال بالإنترنت
    results['network_connection'] = await _checkNetworkConnection();
    
    // 4. فحص Firestore
    results['firestore_status'] = await _checkFirestoreStatus();
    
    // 5. فحص Firebase Auth
    results['auth_status'] = await _checkAuthStatus();
    
    // 6. اقتراحات الحلول
    results['recommendations'] = _generateRecommendations(results);
    
    _printDiagnosticResults(results);
    
    return results;
  }

  static Future<bool> _checkFirebaseInitialization() async {
    try {
      print('🔄 فحص تهيئة Firebase...');
      
      final apps = Firebase.apps;
      if (apps.isNotEmpty) {
        print('✅ Firebase مُهيأ بنجاح');
        print('   التطبيقات المُهيأة: ${apps.length}');
        return true;
      } else {
        print('❌ Firebase غير مُهيأ');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في فحص تهيئة Firebase: $e');
      return false;
    }
  }

  static Future<Map<String, String>> _checkFirebaseConfig() async {
    try {
      print('🔄 فحص إعدادات Firebase...');
      
      final options = DefaultFirebaseOptions.currentPlatform;
      final config = {
        'project_id': options.projectId,
        'app_id': options.appId,
        'api_key': '${options.apiKey.substring(0, 10)}...',
        'auth_domain': options.authDomain ?? 'غير محدد',
        'storage_bucket': options.storageBucket ?? 'غير محدد',
      };
      
      print('✅ إعدادات Firebase:');
      config.forEach((key, value) {
        print('   $key: $value');
      });
      
      // فحص القيم المطلوبة
      if (options.appId.contains('REPLACE_WITH_ACTUAL')) {
        print('⚠️ App ID يحتوي على قيمة وهمية!');
      }
      
      return config;
    } catch (e) {
      print('❌ خطأ في فحص إعدادات Firebase: $e');
      return {'error': e.toString()};
    }
  }

  static Future<bool> _checkNetworkConnection() async {
    try {
      print('🔄 فحص الاتصال بالإنترنت...');
      
      // محاولة بسيطة للاتصال بـ Google
      await _firestore.enableNetwork();
      
      print('✅ الاتصال بالإنترنت يعمل');
      return true;
    } catch (e) {
      print('❌ مشكلة في الاتصال بالإنترنت: $e');
      return false;
    }
  }

  static Future<Map<String, dynamic>> _checkFirestoreStatus() async {
    try {
      print('🔄 فحص حالة Firestore...');
      
      final status = <String, dynamic>{};
      
      // فحص الاتصال الأساسي
      try {
        await _firestore.enableNetwork();
        status['network_enabled'] = true;
      } catch (e) {
        status['network_enabled'] = false;
        status['network_error'] = e.toString();
      }
      
      // محاولة قراءة بسيطة
      try {
        final doc = await _firestore
            .collection('test')
            .doc('diagnostic')
            .get()
            .timeout(const Duration(seconds: 10));
        
        status['read_test'] = true;
        status['document_exists'] = doc.exists;
      } on TimeoutException {
        status['read_test'] = false;
        status['error'] = 'timeout';
      } catch (e) {
        status['read_test'] = false;
        status['error'] = e.toString();
        
        if (e.toString().contains('permission-denied')) {
          status['permission_issue'] = true;
        }
      }
      
      print('✅ فحص Firestore مكتمل');
      return status;
    } catch (e) {
      print('❌ خطأ في فحص Firestore: $e');
      return {'error': e.toString()};
    }
  }

  static Future<Map<String, dynamic>> _checkAuthStatus() async {
    try {
      print('🔄 فحص حالة Firebase Auth...');
      
      final status = <String, dynamic>{};
      
      // فحص المستخدم الحالي
      final currentUser = _auth.currentUser;
      status['current_user'] = currentUser?.email ?? 'غير مسجل دخول';
      status['user_authenticated'] = currentUser != null;
      
      // فحص إعدادات Auth
      status['auth_domain'] = DefaultFirebaseOptions.currentPlatform.authDomain;
      
      print('✅ فحص Firebase Auth مكتمل');
      return status;
    } catch (e) {
      print('❌ خطأ في فحص Firebase Auth: $e');
      return {'error': e.toString()};
    }
  }

  static List<String> _generateRecommendations(Map<String, dynamic> results) {
    final recommendations = <String>[];
    
    // توصيات بناءً على النتائج
    if (results['firebase_initialized'] != true) {
      recommendations.add('🔧 تحقق من تهيئة Firebase في main.dart');
    }
    
    final firestoreStatus = results['firestore_status'] as Map<String, dynamic>?;
    if (firestoreStatus != null) {
      if (firestoreStatus['permission_issue'] == true) {
        recommendations.add('🔐 تحقق من قواعد Firestore - قد تكون مقيدة جداً');
      }
      
      if (firestoreStatus['error'] == 'timeout') {
        recommendations.add('⏱️ مشكلة في المهلة الزمنية - تحقق من إعدادات Firebase');
      }
      
      if (firestoreStatus['read_test'] != true) {
        recommendations.add('📖 مشكلة في قراءة Firestore - تحقق من الإعدادات');
      }
    }
    
    final config = results['firebase_config'] as Map<String, String>?;
    if (config != null && config['app_id']?.contains('REPLACE') == true) {
      recommendations.add('🆔 استبدل App ID بالقيمة الحقيقية من Firebase Console');
    }
    
    if (results['network_connection'] != true) {
      recommendations.add('🌐 تحقق من اتصال الإنترنت');
    }
    
    return recommendations;
  }

  static void _printDiagnosticResults(Map<String, dynamic> results) {
    print('\n📊 ملخص نتائج التشخيص:');
    print('=' * 50);
    
    results.forEach((key, value) {
      if (key != 'recommendations') {
        final status = value is bool 
            ? (value ? '✅ يعمل' : '❌ لا يعمل')
            : '📋 تفاصيل متاحة';
        print('$key: $status');
      }
    });
    
    final recommendations = results['recommendations'] as List<String>?;
    if (recommendations != null && recommendations.isNotEmpty) {
      print('\n💡 التوصيات:');
      for (final rec in recommendations) {
        print('   $rec');
      }
    }
    
    print('=' * 50);
  }
}
