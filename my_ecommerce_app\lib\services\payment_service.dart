import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class PaymentService extends GetxController {
  static PaymentService get instance => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // حالة الدفع
  final RxBool isProcessingPayment = false.obs;
  final RxString paymentStatus = ''.obs;

  // طرق الدفع المتاحة
  final List<PaymentMethod> availablePaymentMethods = [
    PaymentMethod(
      id: 'cash_on_delivery',
      name: 'الدفع عند الاستلام',
      nameEn: 'Cash on Delivery',
      icon: '💵',
      isEnabled: true,
      fees: 0,
      description: 'ادفع نقداً عند استلام طلبك',
    ),
    PaymentMethod(
      id: 'bank_transfer',
      name: 'تحويل بنكي',
      nameEn: 'Bank Transfer',
      icon: '🏦',
      isEnabled: true,
      fees: 0,
      description: 'حول المبلغ إلى حسابنا البنكي',
    ),
    PaymentMethod(
      id: 'ccp',
      name: 'بريد الجزائر CCP',
      nameEn: 'Algeria Post CCP',
      icon: '📮',
      isEnabled: true,
      fees: 0,
      description: 'الدفع عبر حساب بريد الجزائر',
    ),
    PaymentMethod(
      id: 'edahabia',
      name: 'الذهبية',
      nameEn: 'Edahabia',
      icon: '💳',
      isEnabled: true,
      fees: 50,
      description: 'الدفع بالبطاقة الذهبية',
    ),
    PaymentMethod(
      id: 'paypal',
      name: 'PayPal',
      nameEn: 'PayPal',
      icon: '🌐',
      isEnabled: false, // سيتم تفعيلها لاحقاً
      fees: 0,
      description: 'الدفع عبر PayPal',
    ),
  ];

  // معلومات الحسابات البنكية
  final Map<String, BankAccount> bankAccounts = {
    'ccp': BankAccount(
      accountName: 'متجر النظارات العصرية',
      accountNumber: '**********',
      bankName: 'بريد الجزائر',
      rib: '0079999900**********',
      instructions:
          'يرجى إرسال إيصال التحويل عبر WhatsApp أو البريد الإلكتروني',
    ),
    'bank_transfer': BankAccount(
      accountName: 'متجر النظارات العصرية',
      accountNumber: '**********',
      bankName: 'البنك الوطني الجزائري',
      rib: '0079999900**********',
      instructions: 'يرجى ذكر رقم الطلب في وصف التحويل',
    ),
  };

  // معالجة الدفع
  Future<PaymentResult> processPayment({
    required String orderId,
    required double amount,
    required String paymentMethodId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      isProcessingPayment.value = true;
      paymentStatus.value = 'جاري معالجة الدفع...';

      final paymentMethod = availablePaymentMethods
          .firstWhere((method) => method.id == paymentMethodId);

      // إنشاء سجل دفع
      final paymentRecord = PaymentRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        orderId: orderId,
        amount: amount,
        fees: paymentMethod.fees,
        totalAmount: amount + paymentMethod.fees,
        paymentMethodId: paymentMethodId,
        paymentMethodName: paymentMethod.name,
        status: PaymentStatus.pending,
        createdAt: DateTime.now(),
        additionalData: additionalData ?? {},
      );

      // حفظ في قاعدة البيانات
      await _firestore
          .collection('payments')
          .doc(paymentRecord.id)
          .set(paymentRecord.toMap());

      // معالجة حسب طريقة الدفع
      PaymentResult result;
      switch (paymentMethodId) {
        case 'cash_on_delivery':
          result = await _processCashOnDelivery(paymentRecord);
          break;
        case 'bank_transfer':
          result = await _processBankTransfer(paymentRecord);
          break;
        case 'ccp':
          result = await _processCCPTransfer(paymentRecord);
          break;
        case 'edahabia':
          result = await _processEdahabia(paymentRecord);
          break;
        case 'paypal':
          result = await _processPayPal(paymentRecord);
          break;
        default:
          throw Exception('طريقة دفع غير مدعومة');
      }

      // تحديث حالة الدفع
      await _updatePaymentStatus(paymentRecord.id, result.status);

      // تحديث حالة الطلب
      if (result.isSuccess) {
        await _updateOrderStatus(orderId, 'confirmed');
      }

      paymentStatus.value = result.message;
      return result;
    } catch (e) {
      paymentStatus.value = 'خطأ في معالجة الدفع: $e';
      return PaymentResult(
        isSuccess: false,
        status: PaymentStatus.failed,
        message: 'فشل في معالجة الدفع',
        error: e.toString(),
      );
    } finally {
      isProcessingPayment.value = false;
    }
  }

  // الدفع عند الاستلام
  Future<PaymentResult> _processCashOnDelivery(PaymentRecord payment) async {
    // لا يحتاج معالجة فورية
    return PaymentResult(
      isSuccess: true,
      status: PaymentStatus.pending,
      message: 'تم تأكيد الطلب. سيتم الدفع عند الاستلام.',
      paymentId: payment.id,
    );
  }

  // التحويل البنكي
  Future<PaymentResult> _processBankTransfer(PaymentRecord payment) async {
    return PaymentResult(
      isSuccess: true,
      status: PaymentStatus.pending,
      message: 'يرجى إجراء التحويل البنكي وإرسال الإيصال.',
      paymentId: payment.id,
      additionalInfo: {
        'bankAccount': bankAccounts['bank_transfer']?.toMap(),
        'instructions': 'قم بالتحويل إلى الحساب المذكور وأرسل صورة الإيصال',
      },
    );
  }

  // بريد الجزائر CCP
  Future<PaymentResult> _processCCPTransfer(PaymentRecord payment) async {
    return PaymentResult(
      isSuccess: true,
      status: PaymentStatus.pending,
      message: 'يرجى إجراء التحويل عبر بريد الجزائر وإرسال الإيصال.',
      paymentId: payment.id,
      additionalInfo: {
        'ccpAccount': bankAccounts['ccp']?.toMap(),
        'instructions': 'قم بالتحويل إلى حساب CCP المذكور وأرسل صورة الإيصال',
      },
    );
  }

  // البطاقة الذهبية
  Future<PaymentResult> _processEdahabia(PaymentRecord payment) async {
    // هنا يمكن تكامل مع API البطاقة الذهبية
    return PaymentResult(
      isSuccess: true,
      status: PaymentStatus.pending,
      message: 'يرجى الدفع بالبطاقة الذهبية وإرسال الإيصال.',
      paymentId: payment.id,
    );
  }

  // PayPal
  Future<PaymentResult> _processPayPal(PaymentRecord payment) async {
    // هنا يمكن تكامل مع PayPal API
    throw UnimplementedError('PayPal غير متاح حالياً');
  }

  // تحديث حالة الدفع
  Future<void> _updatePaymentStatus(
      String paymentId, PaymentStatus status) async {
    await _firestore.collection('payments').doc(paymentId).update({
      'status': status.toString(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  // تحديث حالة الطلب
  Future<void> _updateOrderStatus(String orderId, String status) async {
    await _firestore.collection('orders').doc(orderId).update({
      'status': status,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  // تأكيد الدفع (للأدمن)
  Future<void> confirmPayment(String paymentId) async {
    try {
      await _updatePaymentStatus(paymentId, PaymentStatus.completed);

      // الحصول على معلومات الدفع
      final paymentDoc =
          await _firestore.collection('payments').doc(paymentId).get();
      if (paymentDoc.exists) {
        final payment = PaymentRecord.fromMap(paymentDoc.data()!);
        await _updateOrderStatus(payment.orderId, 'paid');

        // إرسال تنبيه للعميل
        // await NotificationService.instance.sendNotificationToUser(...)
      }
    } catch (e) {
      throw Exception('خطأ في تأكيد الدفع: $e');
    }
  }

  // رفض الدفع (للأدمن)
  Future<void> rejectPayment(String paymentId, String reason) async {
    try {
      await _firestore.collection('payments').doc(paymentId).update({
        'status': PaymentStatus.failed.toString(),
        'rejectionReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // الحصول على معلومات الدفع
      final paymentDoc =
          await _firestore.collection('payments').doc(paymentId).get();
      if (paymentDoc.exists) {
        final payment = PaymentRecord.fromMap(paymentDoc.data()!);
        await _updateOrderStatus(payment.orderId, 'payment_failed');

        // إرسال تنبيه للعميل
        // await NotificationService.instance.sendNotificationToUser(...)
      }
    } catch (e) {
      throw Exception('خطأ في رفض الدفع: $e');
    }
  }

  // الحصول على سجلات الدفع
  Future<List<PaymentRecord>> getPaymentHistory({String? userId}) async {
    try {
      Query query = _firestore.collection('payments');

      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      final querySnapshot =
          await query.orderBy('createdAt', descending: true).get();

      return querySnapshot.docs
          .map((doc) =>
              PaymentRecord.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('خطأ في جلب سجلات الدفع: $e');
    }
  }

  // الحصول على إحصائيات الدفع
  Future<PaymentStats> getPaymentStats() async {
    try {
      final paymentsSnapshot = await _firestore.collection('payments').get();

      double totalRevenue = 0;
      int totalTransactions = paymentsSnapshot.docs.length;
      int successfulTransactions = 0;
      int pendingTransactions = 0;
      int failedTransactions = 0;

      for (var doc in paymentsSnapshot.docs) {
        final payment = PaymentRecord.fromMap(doc.data());

        if (payment.status == PaymentStatus.completed) {
          totalRevenue += payment.totalAmount;
          successfulTransactions++;
        } else if (payment.status == PaymentStatus.pending) {
          pendingTransactions++;
        } else if (payment.status == PaymentStatus.failed) {
          failedTransactions++;
        }
      }

      return PaymentStats(
        totalRevenue: totalRevenue,
        totalTransactions: totalTransactions,
        successfulTransactions: successfulTransactions,
        pendingTransactions: pendingTransactions,
        failedTransactions: failedTransactions,
        successRate: totalTransactions > 0
            ? (successfulTransactions / totalTransactions) * 100
            : 0,
      );
    } catch (e) {
      throw Exception('خطأ في جلب إحصائيات الدفع: $e');
    }
  }
}

// نماذج البيانات
class PaymentMethod {
  final String id;
  final String name;
  final String nameEn;
  final String icon;
  final bool isEnabled;
  final double fees;
  final String description;

  PaymentMethod({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.icon,
    required this.isEnabled,
    required this.fees,
    required this.description,
  });
}

class BankAccount {
  final String accountName;
  final String accountNumber;
  final String bankName;
  final String rib;
  final String instructions;

  BankAccount({
    required this.accountName,
    required this.accountNumber,
    required this.bankName,
    required this.rib,
    required this.instructions,
  });

  Map<String, dynamic> toMap() {
    return {
      'accountName': accountName,
      'accountNumber': accountNumber,
      'bankName': bankName,
      'rib': rib,
      'instructions': instructions,
    };
  }
}

class PaymentRecord {
  final String id;
  final String orderId;
  final double amount;
  final double fees;
  final double totalAmount;
  final String paymentMethodId;
  final String paymentMethodName;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> additionalData;
  final String? rejectionReason;

  PaymentRecord({
    required this.id,
    required this.orderId,
    required this.amount,
    required this.fees,
    required this.totalAmount,
    required this.paymentMethodId,
    required this.paymentMethodName,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    required this.additionalData,
    this.rejectionReason,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'orderId': orderId,
      'amount': amount,
      'fees': fees,
      'totalAmount': totalAmount,
      'paymentMethodId': paymentMethodId,
      'paymentMethodName': paymentMethodName,
      'status': status.toString(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'additionalData': additionalData,
      'rejectionReason': rejectionReason,
    };
  }

  factory PaymentRecord.fromMap(Map<String, dynamic> map) {
    return PaymentRecord(
      id: map['id'] ?? '',
      orderId: map['orderId'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
      fees: (map['fees'] ?? 0.0).toDouble(),
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      paymentMethodId: map['paymentMethodId'] ?? '',
      paymentMethodName: map['paymentMethodName'] ?? '',
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => PaymentStatus.pending,
      ),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
      additionalData: Map<String, dynamic>.from(map['additionalData'] ?? {}),
      rejectionReason: map['rejectionReason'],
    );
  }
}

class PaymentResult {
  final bool isSuccess;
  final PaymentStatus status;
  final String message;
  final String? paymentId;
  final String? error;
  final Map<String, dynamic>? additionalInfo;

  PaymentResult({
    required this.isSuccess,
    required this.status,
    required this.message,
    this.paymentId,
    this.error,
    this.additionalInfo,
  });
}

class PaymentStats {
  final double totalRevenue;
  final int totalTransactions;
  final int successfulTransactions;
  final int pendingTransactions;
  final int failedTransactions;
  final double successRate;

  PaymentStats({
    required this.totalRevenue,
    required this.totalTransactions,
    required this.successfulTransactions,
    required this.pendingTransactions,
    required this.failedTransactions,
    required this.successRate,
  });
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}
