rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() &&
        exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 5 * 1024 * 1024; // 5MB max
    }
    
    // صور المنتجات - فقط الأدمن يمكنه رفعها
    match /products/{productId}/{allPaths=**} {
      allow read: if true; // الجميع يمكنهم قراءة صور المنتجات
      allow write: if isAdmin() && isValidImageFile();
    }
    
    // صور المستخدمين - كل مستخدم يمكنه رفع صورته الشخصية
    match /users/{userId}/profile/{allPaths=**} {
      allow read: if true; // الجميع يمكنهم قراءة الصور الشخصية
      allow write: if isOwner(userId) && isValidImageFile();
    }
    
    // صور الفئات - فقط الأدمن يمكنه رفعها
    match /categories/{categoryId}/{allPaths=**} {
      allow read: if true; // الجميع يمكنهم قراءة صور الفئات
      allow write: if isAdmin() && isValidImageFile();
    }
    
    // ملفات مؤقتة للاختبار
    match /temp/{allPaths=**} {
      allow read, write: if isAuthenticated() && isValidImageFile();
    }
    
    // رفض الوصول لأي مسار آخر
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
