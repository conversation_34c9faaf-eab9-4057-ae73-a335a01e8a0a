---
description: Repository Information Overview
alwaysApply: true
---

# My Ecommerce App Information

## Summary
A Flutter-based e-commerce application for a modern eyewear store. The app supports multiple platforms (Android, iOS, web, desktop) and includes features for customers, affiliates, and administrators. It uses Firebase for authentication and data storage.

## Structure
- **lib/**: Core application code (screens, services, controllers, models)
- **android/**, **ios/**, **web/**, **linux/**, **macos/**, **windows/**: Platform-specific code
- **assets/**: Application resources (images)
- **test/**: Testing files

## Language & Runtime
**Language**: Dart
**Version**: SDK >=3.3.1 <4.0.0
**Framework**: Flutter >=3.19.0
**Package Manager**: pub (Flutter/Dart package manager)

## Dependencies
**Main Dependencies**:
- get: ^4.6.6 (State management and routing)
- firebase_core: ^2.32.0 (Firebase integration)
- cloud_firestore: ^4.17.0 (Database)
- firebase_auth: ^4.17.5 (Authentication)
- http: 1.2.2 (Network requests)
- flutter_svg: ^2.0.7 (SVG rendering)
- google_fonts: ^6.1.0 (Typography)
- provider: ^6.1.2 (State management alternative)
- shared_preferences: ^2.2.2 (Local storage)

**Development Dependencies**:
- flutter_test (Testing framework)
- flutter_lints: ^3.0.0 (Code quality)

## Build & Installation
```bash
# Install dependencies
flutter pub get

# Run the application in debug mode
flutter run

# Build for specific platforms
flutter build apk        # Android
flutter build ios        # iOS
flutter build web        # Web
flutter build windows    # Windows
flutter build macos      # macOS
flutter build linux      # Linux
```

## Application Structure
**Entry Point**: lib/main.dart
**State Management**: GetX (primary), Provider (secondary)
**Architecture**: Service-Controller pattern
- **Services**: Authentication, Cart, Wishlist, Admin, Affiliate
- **Controllers**: Auth, Admin, Affiliate, Cart
- **Models**: User, Product, Cart, Order, Wishlist, AffiliateLink
- **Screens**: Home, Auth, Cart, Admin, Affiliate, Settings, Search, Wishlist

## Features
- User authentication (login, register, password reset)
- Product browsing and search
- Shopping cart and checkout
- Wishlist management
- Affiliate marketing system
- Admin dashboard
- Multi-platform support (responsive design)
- Arabic language support

## Testing
**Framework**: flutter_test
**Test Location**: test/
**Run Command**:
```bash
flutter test
```