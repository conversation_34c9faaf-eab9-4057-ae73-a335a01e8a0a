import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// خدمة اختبار الاتصال بقاعدة البيانات
class DatabaseTestService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// اختبار الاتصال بـ Firestore
  Future<bool> testFirestoreConnection() async {
    try {
      print('🔄 اختبار الاتصال بـ Firestore...');

      // محاولة بسيطة للاتصال بـ Firestore
      await _firestore.enableNetwork();

      // محاولة قراءة مجموعة test مع timeout أقصر
      await _firestore
          .collection('test')
          .doc('connection_test')
          .get()
          .timeout(const Duration(seconds: 15));

      print('✅ تم الاتصال بـ Firestore بنجاح');
      return true;
    } on TimeoutException catch (e) {
      print('❌ انتهت مهلة الاتصال بـ Firestore: $e');
      print('💡 تحقق من إعدادات Firebase وقواعد Firestore');
      return false;
    } catch (e) {
      print('❌ فشل الاتصال بـ Firestore: $e');
      if (e.toString().contains('permission-denied')) {
        print('💡 مشكلة في صلاحيات Firestore - تحقق من القواعد');
      } else if (e.toString().contains('unavailable')) {
        print('💡 Firestore غير متوفر - تحقق من الاتصال بالإنترنت');
      }
      return false;
    }
  }

  /// اختبار كتابة البيانات في Firestore
  Future<bool> testFirestoreWrite() async {
    try {
      print('🔄 اختبار كتابة البيانات في Firestore...');

      final testData = {
        'timestamp': FieldValue.serverTimestamp(),
        'test_message': 'اختبار الكتابة في قاعدة البيانات',
        'app_version': '1.0.0',
      };

      await _firestore
          .collection('test')
          .doc('write_test')
          .set(testData)
          .timeout(const Duration(seconds: 10));

      print('✅ تم كتابة البيانات في Firestore بنجاح');
      return true;
    } catch (e) {
      print('❌ فشل كتابة البيانات في Firestore: $e');
      return false;
    }
  }

  /// اختبار قراءة البيانات من Firestore
  Future<bool> testFirestoreRead() async {
    try {
      print('🔄 اختبار قراءة البيانات من Firestore...');

      final doc = await _firestore
          .collection('test')
          .doc('write_test')
          .get()
          .timeout(const Duration(seconds: 10));

      if (doc.exists) {
        final data = doc.data();
        print(
            '✅ تم قراءة البيانات من Firestore بنجاح: ${data?['test_message']}');
        return true;
      } else {
        print('⚠️ المستند غير موجود في Firestore');
        return false;
      }
    } catch (e) {
      print('❌ فشل قراءة البيانات من Firestore: $e');
      return false;
    }
  }

  /// اختبار Firebase Authentication
  Future<bool> testFirebaseAuth() async {
    try {
      print('🔄 اختبار Firebase Authentication...');

      // التحقق من حالة المصادقة الحالية
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        print('✅ المستخدم مسجل دخول: ${currentUser.email}');
        return true;
      } else {
        print('ℹ️ لا يوجد مستخدم مسجل دخول حالياً');
        return true; // هذا طبيعي إذا لم يسجل المستخدم دخوله
      }
    } catch (e) {
      print('❌ فشل اختبار Firebase Authentication: $e');
      return false;
    }
  }

  /// تشغيل جميع الاختبارات
  Future<Map<String, bool>> runAllTests() async {
    print('🚀 بدء اختبار جميع خدمات قاعدة البيانات...\n');

    final results = <String, bool>{};

    // اختبار الاتصال
    results['firestore_connection'] = await testFirestoreConnection();
    await Future.delayed(const Duration(seconds: 1));

    // اختبار الكتابة
    results['firestore_write'] = await testFirestoreWrite();
    await Future.delayed(const Duration(seconds: 1));

    // اختبار القراءة
    results['firestore_read'] = await testFirestoreRead();
    await Future.delayed(const Duration(seconds: 1));

    // اختبار المصادقة
    results['firebase_auth'] = await testFirebaseAuth();

    print('\n📊 نتائج الاختبارات:');
    results.forEach((test, result) {
      final icon = result ? '✅' : '❌';
      print('$icon $test: ${result ? 'نجح' : 'فشل'}');
    });

    final allPassed = results.values.every((result) => result);
    print(
        '\n${allPassed ? '🎉' : '⚠️'} النتيجة النهائية: ${allPassed ? 'جميع الاختبارات نجحت' : 'بعض الاختبارات فشلت'}');

    return results;
  }

  /// تنظيف بيانات الاختبار
  Future<void> cleanupTestData() async {
    try {
      print('🧹 تنظيف بيانات الاختبار...');

      await _firestore.collection('test').doc('connection_test').delete();
      await _firestore.collection('test').doc('write_test').delete();

      print('✅ تم تنظيف بيانات الاختبار بنجاح');
    } catch (e) {
      print('⚠️ تعذر تنظيف بيانات الاختبار: $e');
    }
  }

  /// عرض معلومات الاتصال
  void showConnectionInfo() {
    print('📋 معلومات الاتصال:');
    print('- Project ID: my-ecommerce-app-61373');
    print('- Auth Domain: my-ecommerce-app-61373.firebaseapp.com');
    print('- Storage Bucket: my-ecommerce-app-61373.appspot.com');
    print('- Current User: ${_auth.currentUser?.email ?? 'غير مسجل دخول'}');
  }
}
