import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/category.dart';
import '../widgets/enhanced_product_card.dart';
import '../utils/mouse_event_handler.dart';
import '../screens/category/category_screen.dart';
import '../controllers/home_controller.dart';

// شريط الإعلانات المتحرك
class AnnouncementBanner extends StatefulWidget {
  const AnnouncementBanner({super.key});

  @override
  State<AnnouncementBanner> createState() => _AnnouncementBannerState();
}

class _AnnouncementBannerState extends State<AnnouncementBanner>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _animation;

  final List<String> announcements = [
    '🔥 خصم 30% على جميع النظارات الشمسية!',
    '🚚 شحن مجاني للطلبات فوق 15,000 دج',
    '⭐ منتجات جديدة وصلت حديثاً',
    '💎 جودة عالية وضمان سنة كاملة',
  ];

  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _animation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: const Offset(-1.0, 0.0),
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    _startAnimation();
  }

  void _startAnimation() {
    _controller.forward().then((_) {
      setState(() {
        currentIndex = (currentIndex + 1) % announcements.length;
      });
      _controller.reset();
      _startAnimation();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.red.shade400, Colors.orange.shade400],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: Center(
        child: SlideTransition(
          position: _animation,
          child: Text(
            announcements[currentIndex],
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

// بانر الترويج الرئيسي
class HeroBanner extends StatelessWidget {
  const HeroBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180, // زيادة الارتفاع لتجنب overflow
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [Colors.teal.shade600, Colors.teal.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.teal.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Stack(
        children: [
          // خلفية زخرفية
          Positioned(
            right: -50,
            top: -50,
            child: Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
          ),
          Positioned(
            left: -30,
            bottom: -30,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
          ),

          // المحتوى
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'مجموعة حصرية',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Flexible(
                        child: Text(
                          'نظارات عصرية بأفضل الأسعار',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            height: 1.1,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () => _scrollToProducts(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.teal,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          minimumSize: const Size(0, 32),
                        ),
                        child: const Text(
                          'تسوق الآن',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // صورة النظارة
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.2),
                  ),
                  child: const Icon(
                    Icons.visibility,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _scrollToProducts() {
    // يمكن إضافة منطق التمرير للمنتجات
    Get.snackbar('تسوق الآن', 'مرحباً بك في متجرنا!');
  }
}

// قسم الفئات المتطور
class CategoriesSection extends StatelessWidget {
  const CategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController homeController = Get.find<HomeController>();

    return Container(
      color: Colors.grey[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'تسوق حسب الفئة',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                TextButton(
                  onPressed: () => _showAllCategories(),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // الفئات الرئيسية - تصميم مضغوط
            Obx(() => SizedBox(
                  height: 80,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: homeController.categories.length,
                    itemBuilder: (context, index) {
                      final category = homeController.categories[index];
                      return _buildCompactCategoryCard(category);
                    },
                  ),
                )),

            const SizedBox(height: 16),

            // الفئات الفرعية الشائعة - تصميم مضغوط
            const Text(
              'الفئات الشائعة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),

            _buildCompactSubCategories(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactCategoryCard(Category category) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12),
      child: SafeGestureDetector(
        onTap: () => _onCategoryTap(category),
        child: Column(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: _getCategoryColor(category.type).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getCategoryColor(category.type).withValues(alpha: 0.3),
                  width: 1.5,
                ),
              ),
              child: Center(
                child: Text(
                  category.icon,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              category.name,
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactSubCategories() {
    final popularSubs = [
      {
        'category': 'glasses',
        'subId': 'sunglasses_men',
        'name': 'نظارات شمسية رجالية',
        'icon': '🕶️'
      },
      {
        'category': 'glasses',
        'subId': 'sunglasses_women',
        'name': 'نظارات شمسية نسائية',
        'icon': '🕶️'
      },
      {
        'category': 'glasses',
        'subId': 'prescription_men',
        'name': 'نظارات طبية رجالية',
        'icon': '👓'
      },
      {
        'category': 'glasses',
        'subId': 'prescription_women',
        'name': 'نظارات طبية نسائية',
        'icon': '👓'
      },
    ];

    return SizedBox(
      height: 35,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: popularSubs.length,
        itemBuilder: (context, index) {
          final sub = popularSubs[index];
          return _buildCompactSubCategoryChip(sub);
        },
      ),
    );
  }

  Widget _buildCompactSubCategoryChip(Map<String, String> sub) {
    return SafeGestureDetector(
      onTap: () => _onSubCategoryTap(sub['category']!, sub['subId']!),
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: Colors.grey[300]!),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              sub['icon']!,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(width: 4),
            Text(
              sub['name']!,
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor(CategoryType type) {
    switch (type) {
      case CategoryType.glasses:
        return Colors.teal;
      case CategoryType.accessories:
        return Colors.orange;
      case CategoryType.lenses:
        return Colors.blue;
      case CategoryType.services:
        return Colors.green;
      case CategoryType.content:
        return Colors.purple;
    }
  }

  void _onCategoryTap(Category category) {
    Get.to(() => CategoryScreen(category: category));
  }

  void _onSubCategoryTap(String categoryId, String subCategoryId) {
    final category =
        Category.demoCategories.firstWhere((c) => c.id == categoryId);
    final subCategory =
        category.subCategories.firstWhere((s) => s.id == subCategoryId);

    Get.to(() => CategoryScreen(
          category: category,
          subCategory: subCategory,
        ));
  }

  void _showAllCategories() {
    Get.snackbar(
      'جميع الفئات',
      'سيتم عرض صفحة جميع الفئات قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

// قسم المنتجات المميزة
class FeaturedProductsSection extends StatelessWidget {
  const FeaturedProductsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController homeController = Get.find<HomeController>();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Row(
                children: [
                  Text(
                    '⭐',
                    style: TextStyle(fontSize: 20),
                  ),
                  SizedBox(width: 8),
                  Text(
                    'منتجات مميزة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () =>
                    Get.snackbar('عرض الكل', 'سيتم فتح صفحة جميع المنتجات'),
                child: const Text(
                  'عرض الكل',
                  style: TextStyle(
                    color: Colors.teal,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // عرض المنتجات في Grid
          Obx(() {
            if (homeController.isLoadingFeatured.value) {
              return SizedBox(
                height: 200,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                        strokeWidth: 3,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'جاري تحميل المنتجات المميزة...',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            if (homeController.featuredProducts.isEmpty) {
              return SizedBox(
                height: 200,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.shopping_bag_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد منتجات مميزة حالياً',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'سيتم إضافة منتجات جديدة قريباً',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: _getCrossAxisCount(context),
                childAspectRatio:
                    _getChildAspectRatio(context), // نسبة العرض للارتفاع محسنة
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: homeController.featuredProducts.length,
              itemBuilder: (context, index) {
                return EnhancedProductCard(
                  product: homeController.featuredProducts[index],
                  isGridView: true,
                  showQuantityControls: true,
                );
              },
            );
          }),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2; // للهاتف المحمول
  }

  double _getChildAspectRatio(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 0.9; // سطح المكتب - أوسع
    if (width > 800) return 0.85; // تابلت
    return 0.8; // هاتف محمول - نسبة محسنة للارتفاع الثابت
  }
}

// قسم المزايا
class FeaturesSection extends StatelessWidget {
  const FeaturesSection({super.key});

  @override
  Widget build(BuildContext context) {
    final features = [
      {
        'icon': Icons.local_shipping,
        'title': 'شحن مجاني',
        'subtitle': 'للطلبات فوق 15,000 دج',
        'color': Colors.green,
      },
      {
        'icon': Icons.payment,
        'title': 'دفع آمن',
        'subtitle': 'الدفع عند الاستلام',
        'color': Colors.blue,
      },
      {
        'icon': Icons.verified,
        'title': 'ضمان الجودة',
        'subtitle': 'ضمان سنة كاملة',
        'color': Colors.orange,
      },
      {
        'icon': Icons.support_agent,
        'title': 'دعم 24/7',
        'subtitle': 'خدمة عملاء متميزة',
        'color': Colors.purple,
      },
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'لماذا تختارنا؟',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: features.length,
            itemBuilder: (context, index) {
              final feature = features[index];
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      feature['icon'] as IconData,
                      size: 32,
                      color: feature['color'] as Color,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      feature['title'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      feature['subtitle'] as String,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

// قسم العلامات التجارية المشهورة
class BrandsSection extends StatelessWidget {
  const BrandsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final brands = [
      {'name': 'Ray-Ban', 'logo': '🕶️', 'color': Colors.black},
      {'name': 'Gucci', 'logo': '👑', 'color': Colors.green},
      {'name': 'Prada', 'logo': '💎', 'color': Colors.red},
      {'name': 'Oakley', 'logo': '🏃‍♂️', 'color': Colors.blue},
      {'name': 'Versace', 'logo': '✨', 'color': Colors.purple},
      {'name': 'Tom Ford', 'logo': '🎩', 'color': Colors.brown},
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'العلامات التجارية المشهورة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: brands.length,
              itemBuilder: (context, index) {
                final brand = brands[index];
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: 12),
                  child: SafeGestureDetector(
                    onTap: () => _onBrandTap(brand['name'] as String),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            spreadRadius: 1,
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            brand['logo'] as String,
                            style: const TextStyle(fontSize: 24),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            brand['name'] as String,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: brand['color'] as Color,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _onBrandTap(String brandName) {
    Get.snackbar(
      'العلامة التجارية',
      'تصفح منتجات $brandName',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

// قسم العروض والخصومات
class OffersSection extends StatelessWidget {
  const OffersSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      height: 120, // تقليل الارتفاع
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.red.shade400, Colors.orange.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          Positioned(
            right: 20,
            top: 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '🔥 عروض حصرية',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'خصم يصل إلى 50%',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: () => _viewOffers(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.red,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text('تسوق الآن'),
                ),
              ],
            ),
          ),
          Positioned(
            left: -20,
            bottom: -20,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
          const Positioned(
            left: 20,
            bottom: 20,
            child: Text(
              '🕶️',
              style: TextStyle(fontSize: 60),
            ),
          ),
        ],
      ),
    );
  }

  void _viewOffers() {
    Get.snackbar(
      'العروض',
      'تصفح جميع العروض والخصومات',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

// قسم المحتوى التوعوي
class EducationalContentSection extends StatelessWidget {
  const EducationalContentSection({super.key});

  @override
  Widget build(BuildContext context) {
    final articles = [
      {
        'title': 'كيف تختار النظارة المناسبة لشكل وجهك؟',
        'image':
            'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=300',
        'readTime': '5 دقائق',
        'category': 'دليل الشراء',
      },
      {
        'title': 'أهمية الحماية من الأشعة الزرقاء',
        'image':
            'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=300',
        'readTime': '3 دقائق',
        'category': 'صحة العيون',
      },
      {
        'title': 'أحدث صيحات النظارات 2025',
        'image':
            'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=300',
        'readTime': '4 دقائق',
        'category': 'موضة',
      },
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'مقالات مفيدة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => _viewAllArticles(),
                child: const Text('عرض الكل'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: articles.length,
              itemBuilder: (context, index) {
                final article = articles[index];
                return Container(
                  width: 250,
                  margin: const EdgeInsets.only(right: 12),
                  child: SafeGestureDetector(
                    onTap: () => _readArticle(article['title'] as String),
                    child: Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(12),
                            ),
                            child: Container(
                              height: 100,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image:
                                      NetworkImage(article['image'] as String),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.teal.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    article['category'] as String,
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.teal,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  article['title'] as String,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'وقت القراءة: ${article['readTime']}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _viewAllArticles() {
    Get.snackbar(
      'المقالات',
      'تصفح جميع المقالات والنصائح',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _readArticle(String title) {
    Get.snackbar(
      'مقال',
      'قراءة: $title',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

// قسم آراء العملاء
class TestimonialsSection extends StatelessWidget {
  const TestimonialsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final testimonials = [
      {
        'name': 'أحمد محمد',
        'rating': 5,
        'comment':
            'خدمة ممتازة وجودة عالية. النظارات وصلت بسرعة وبحالة ممتازة.',
        'avatar': '👨',
      },
      {
        'name': 'فاطمة علي',
        'rating': 5,
        'comment': 'أفضل متجر نظارات جربته. التصميمات عصرية والأسعار معقولة.',
        'avatar': '👩',
      },
      {
        'name': 'خالد أحمد',
        'rating': 4,
        'comment': 'تجربة رائعة، النظارات مريحة جداً وتناسب شكل وجهي تماماً.',
        'avatar': '👨‍💼',
      },
    ];

    return Container(
      color: Colors.grey[50],
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'آراء عملائنا',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 150,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: testimonials.length,
              itemBuilder: (context, index) {
                final testimonial = testimonials[index];
                return Container(
                  width: 280,
                  margin: const EdgeInsets.only(right: 12),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            testimonial['avatar'] as String,
                            style: const TextStyle(fontSize: 24),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                testimonial['name'] as String,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              Row(
                                children: List.generate(
                                  testimonial['rating'] as int,
                                  (index) => const Icon(
                                    Icons.star,
                                    color: Colors.amber,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: Text(
                          testimonial['comment'] as String,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[700],
                            height: 1.4,
                          ),
                          maxLines: 4,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// قسم الأكثر مبيعاً
class BestSellersSection extends StatelessWidget {
  const BestSellersSection({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController homeController = Get.find<HomeController>();

    return Container(
      color: Colors.grey[50],
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Row(
                children: [
                  Text(
                    '🔥',
                    style: TextStyle(fontSize: 20),
                  ),
                  SizedBox(width: 8),
                  Text(
                    'الأكثر مبيعاً',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () => _viewAllBestSellers(),
                child: const Text(
                  'عرض الكل',
                  style: TextStyle(
                    color: Colors.teal,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // عرض المنتجات في Grid
          Obx(() {
            if (homeController.isLoadingBestSellers.value) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            if (homeController.bestSellerProducts.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'لا توجد منتجات أكثر مبيعاً حالياً',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
              );
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: _getCrossAxisCount(context),
                childAspectRatio: 0.75,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: homeController.bestSellerProducts.length,
              itemBuilder: (context, index) {
                return EnhancedProductCard(
                  product: homeController.bestSellerProducts[index],
                  isGridView: true,
                  showQuantityControls: true,
                );
              },
            );
          }),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2; // للهاتف المحمول
  }

  void _viewAllBestSellers() {
    Get.snackbar(
      'الأكثر مبيعاً',
      'تصفح جميع المنتجات الأكثر مبيعاً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

// قسم المنتجات الموصى بها
class RecommendedProductsSection extends StatelessWidget {
  const RecommendedProductsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController homeController = Get.find<HomeController>();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Row(
                children: [
                  Text(
                    '💎',
                    style: TextStyle(fontSize: 20),
                  ),
                  SizedBox(width: 8),
                  Text(
                    'منتجات موصى بها',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () => _viewAllRecommended(),
                child: const Text(
                  'عرض الكل',
                  style: TextStyle(
                    color: Colors.teal,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // عرض المنتجات في Grid
          Obx(() {
            if (homeController.isLoadingRecommended.value) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            if (homeController.recommendedProducts.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'لا توجد منتجات موصى بها حالياً',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
              );
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: _getCrossAxisCount(context),
                childAspectRatio: 0.75,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: homeController.recommendedProducts.length,
              itemBuilder: (context, index) {
                return EnhancedProductCard(
                  product: homeController.recommendedProducts[index],
                  isGridView: true,
                  showQuantityControls: true,
                );
              },
            );
          }),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2; // للهاتف المحمول
  }

  void _viewAllRecommended() {
    Get.snackbar(
      'المنتجات الموصى بها',
      'تصفح جميع المنتجات الموصى بها',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
