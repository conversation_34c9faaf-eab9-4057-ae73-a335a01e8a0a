import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/user.dart' as app_user;

/// خدمة إعداد حساب الإدمن الأولي
class AdminSetupService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// التحقق من وجود حساب إدمن في النظام
  static Future<bool> hasAdminAccount() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: app_user.UserRole.admin.toString())
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود حساب إدمن: $e');
      return false;
    }
  }

  /// إنشاء حساب إدمن أولي
  static Future<bool> createInitialAdminAccount({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
  }) async {
    try {
      // التحقق من عدم وجود حساب إدمن مسبقاً
      final hasAdmin = await hasAdminAccount();
      if (hasAdmin) {
        debugPrint('يوجد حساب إدمن بالفعل في النظام');
        return false;
      }

      // إنشاء حساب في Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        debugPrint('فشل في إنشاء المستخدم في Firebase Auth');
        return false;
      }

      // إنشاء بيانات المستخدم
      final adminUser = app_user.User(
        id: firebaseUser.uid,
        email: email,
        password: password, // في الإنتاج، لا تحفظ كلمة المرور
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber ?? '',
        role: app_user.UserRole.admin,
        status: app_user.UserStatus.active,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      // حفظ بيانات المستخدم في Firestore
      await _firestore
          .collection('users')
          .doc(firebaseUser.uid)
          .set(adminUser.toJson());

      debugPrint('تم إنشاء حساب الإدمن الأولي بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء حساب الإدمن الأولي: $e');
      return false;
    }
  }

  /// إنشاء حساب إدمن افتراضي للتطوير
  static Future<bool> createDefaultAdminForDevelopment() async {
    return await createInitialAdminAccount(
      email: '<EMAIL>',
      password: 'Admin123!',
      firstName: 'مدير',
      lastName: 'النظام',
      phoneNumber: '+************',
    );
  }

  /// التحقق من صلاحيات الإدمن
  static Future<bool> verifyAdminPermissions(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();

      if (!doc.exists) {
        return false;
      }

      final userData = doc.data();
      if (userData == null) {
        return false;
      }

      final role = userData['role'] as String?;
      return role == app_user.UserRole.admin.toString();
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحيات الإدمن: $e');
      return false;
    }
  }

  /// ترقية مستخدم عادي إلى إدمن (للطوارئ فقط)
  static Future<bool> promoteUserToAdmin(
      String userId, String currentAdminId) async {
    try {
      // التحقق من أن المستخدم الحالي إدمن
      final isCurrentUserAdmin = await verifyAdminPermissions(currentAdminId);
      if (!isCurrentUserAdmin) {
        debugPrint('المستخدم الحالي ليس إدمن');
        return false;
      }

      // ترقية المستخدم
      await _firestore.collection('users').doc(userId).update({
        'role': app_user.UserRole.admin.toString(),
        'promotedAt': DateTime.now().toIso8601String(),
        'promotedBy': currentAdminId,
      });

      debugPrint('تم ترقية المستخدم إلى إدمن بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في ترقية المستخدم إلى إدمن: $e');
      return false;
    }
  }

  /// إلغاء صلاحيات الإدمن (تحويل إلى مستخدم عادي)
  static Future<bool> demoteAdminToUser(
      String adminId, String currentAdminId) async {
    try {
      // التحقق من أن المستخدم الحالي إدمن
      final isCurrentUserAdmin = await verifyAdminPermissions(currentAdminId);
      if (!isCurrentUserAdmin) {
        debugPrint('المستخدم الحالي ليس إدمن');
        return false;
      }

      // منع الإدمن من إلغاء صلاحياته الخاصة
      if (adminId == currentAdminId) {
        debugPrint('لا يمكن للإدمن إلغاء صلاحياته الخاصة');
        return false;
      }

      // التحقق من وجود إدمن آخر في النظام
      final adminCount = await _firestore
          .collection('users')
          .where('role', isEqualTo: app_user.UserRole.admin.toString())
          .count()
          .get();

      if ((adminCount.count ?? 0) <= 1) {
        debugPrint('لا يمكن إلغاء صلاحيات الإدمن الوحيد في النظام');
        return false;
      }

      // إلغاء صلاحيات الإدمن
      await _firestore.collection('users').doc(adminId).update({
        'role': app_user.UserRole.customer.toString(),
        'demotedAt': DateTime.now().toIso8601String(),
        'demotedBy': currentAdminId,
      });

      debugPrint('تم إلغاء صلاحيات الإدمن بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إلغاء صلاحيات الإدمن: $e');
      return false;
    }
  }

  /// جلب جميع المديرين في النظام
  static Future<List<app_user.User>> getAllAdmins() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: app_user.UserRole.admin.toString())
          .get();

      return snapshot.docs
          .map((doc) => app_user.User.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب قائمة المديرين: $e');
      return [];
    }
  }

  /// إنشاء سجل أمان للعمليات الحساسة
  static Future<void> logSecurityAction({
    required String action,
    required String adminId,
    String? targetUserId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final logEntry = {
        'action': action,
        'adminId': adminId,
        'targetUserId': targetUserId,
        'timestamp': FieldValue.serverTimestamp(),
        'additionalData': additionalData,
      };

      await _firestore.collection('security_logs').add(logEntry);
    } catch (e) {
      debugPrint('خطأ في تسجيل العملية الأمنية: $e');
    }
  }

  /// التحقق من قوة كلمة المرور
  static bool isStrongPassword(String password) {
    // يجب أن تحتوي على 8 أحرف على الأقل
    if (password.length < 8) return false;

    // يجب أن تحتوي على حرف كبير
    if (!password.contains(RegExp(r'[A-Z]'))) return false;

    // يجب أن تحتوي على حرف صغير
    if (!password.contains(RegExp(r'[a-z]'))) return false;

    // يجب أن تحتوي على رقم
    if (!password.contains(RegExp(r'[0-9]'))) return false;

    // يجب أن تحتوي على رمز خاص
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) return false;

    return true;
  }

  /// إنشاء كلمة مرور قوية عشوائية
  static String generateStrongPassword() {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#\$%^&*';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(12, (index) => chars[(random + index) % chars.length])
        .join();
  }
}
