import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/database_test_service.dart';
import '../services/firebase_diagnostic_service.dart';

class DatabaseTestScreen extends StatefulWidget {
  const DatabaseTestScreen({super.key});

  @override
  State<DatabaseTestScreen> createState() => _DatabaseTestScreenState();
}

class _DatabaseTestScreenState extends State<DatabaseTestScreen> {
  final DatabaseTestService _testService = DatabaseTestService();
  Map<String, bool>? _testResults;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار قاعدة البيانات'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات الاتصال',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('Project ID: my-ecommerce-app-61373'),
                    Text(
                        'Auth Domain: my-ecommerce-app-61373.firebaseapp.com'),
                    Text(
                        'Storage Bucket: my-ecommerce-app-61373.appspot.com'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isRunning ? null : _runTests,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isRunning
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('جاري تشغيل الاختبارات...'),
                      ],
                    )
                  : const Text(
                      'تشغيل اختبارات قاعدة البيانات',
                      style: TextStyle(fontSize: 16),
                    ),
            ),
            const SizedBox(height: 16),
            if (_testResults != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نتائج الاختبارات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ..._testResults!.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Icon(
                                entry.value ? Icons.check_circle : Icons.error,
                                color: entry.value ? Colors.green : Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _getTestDisplayName(entry.key),
                                  style: TextStyle(
                                    color:
                                        entry.value ? Colors.green : Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Text(
                                entry.value ? 'نجح' : 'فشل',
                                style: TextStyle(
                                  color:
                                      entry.value ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _allTestsPassed()
                              ? Colors.green.shade50
                              : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                _allTestsPassed() ? Colors.green : Colors.red,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _allTestsPassed()
                              ? '🎉 جميع الاختبارات نجحت! التطبيق متصل بقاعدة البيانات بشكل صحيح.'
                              : '⚠️ بعض الاختبارات فشلت. يرجى التحقق من إعدادات Firebase.',
                          style: TextStyle(
                            color: _allTestsPassed()
                                ? Colors.green.shade800
                                : Colors.red.shade800,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _cleanupTestData,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('تنظيف بيانات الاختبار'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _runDiagnostics,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('تشخيص Firebase'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });

    try {
      final results = await _testService.runAllTests();
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تشغيل الاختبارات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _cleanupTestData() async {
    await _testService.cleanupTestData();
    Get.snackbar(
      'تم',
      'تم تنظيف بيانات الاختبار',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
    });

    try {
      await FirebaseDiagnosticService.runDiagnostics();
      Get.snackbar(
        'تم التشخيص',
        'تحقق من Console للتفاصيل',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء التشخيص: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  String _getTestDisplayName(String testKey) {
    switch (testKey) {
      case 'firestore_connection':
        return 'اختبار الاتصال بـ Firestore';
      case 'firestore_write':
        return 'اختبار كتابة البيانات';
      case 'firestore_read':
        return 'اختبار قراءة البيانات';
      case 'firebase_auth':
        return 'اختبار Firebase Authentication';
      default:
        return testKey;
    }
  }

  bool _allTestsPassed() {
    return _testResults?.values.every((result) => result) ?? false;
  }
}
