# 🛠️ دليل تنفيذ نظام المسوق - Implementation Guide

## 🎯 **الأولوية الأولى: الصفحة الرئيسية للمسوق**

### **1. إنشاء AffiliateHomeScreen:**

```dart
// lib/screens/affiliate/affiliate_home_screen.dart
class AffiliateHomeScreen extends StatelessWidget {
  const AffiliateHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AffiliateController>();
    
    return Scaffold(
      appBar: const UserRoleAppBar(title: "الصفحة الرئيسية - المسوق"),
      body: RefreshIndicator(
        onRefresh: () => controller.loadData(),
        child: SingleChildScrollView(
          child: Column(
            children: [
              // 1. شريط الإحصائيات السريعة
              _buildQuickStatsBar(controller),
              
              // 2. الأدوات السريعة
              _buildQuickActionsSection(controller),
              
              // 3. المنتجات المقترحة للتسويق
              _buildRecommendedProducts(controller),
              
              // 4. أداء الروابط الحالية
              _buildLinksPerformance(controller),
              
              // 5. العمولات الأخيرة
              _buildRecentCommissions(controller),
              
              // 6. نصائح تسويقية
              _buildMarketingTips(),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildQuickCreateLinkFAB(controller),
    );
  }
}
```

### **2. تحديث التوجيه للمسوق:**

```dart
// في lib/controllers/auth_controller.dart
void _navigateBasedOnRole() {
  if (currentUser == null) return;

  switch (currentUser!.role) {
    case UserRole.admin:
      Get.offAllNamed('/admin-dashboard');
      break;
    case UserRole.affiliate:
      // تغيير: المسوق يذهب لصفحة رئيسية مخصصة
      Get.offAllNamed('/affiliate-home'); // بدلاً من '/'
      break;
    case UserRole.customer:
      Get.offAllNamed('/');
      break;
  }
}
```

### **3. إضافة الراوت الجديد:**

```dart
// في lib/routes/app_pages.dart
GetPage(
  name: '/affiliate-home',
  page: () => const AffiliateHomeScreen(),
),
```

## 📊 **الأولوية الثانية: إحصائيات سريعة**

### **1. تحسين AffiliateController:**

```dart
// إضافة إحصائيات يومية
class AffiliateController extends GetxController {
  // إحصائيات اليوم
  final RxMap<String, dynamic> todayStats = <String, dynamic>{}.obs;
  
  // إحصائيات الشهر
  final RxMap<String, dynamic> monthStats = <String, dynamic>{}.obs;
  
  // تحميل الإحصائيات اليومية
  Future<void> loadTodayStats() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    
    // جلب إحصائيات اليوم
    todayStats.value = {
      'todayClicks': _getTodayClicks(startOfDay),
      'todayEarnings': _getTodayEarnings(startOfDay),
      'todaySales': _getTodaySales(startOfDay),
      'todayConversionRate': _getTodayConversionRate(startOfDay),
    };
  }
  
  // تحميل الإحصائيات الشهرية
  Future<void> loadMonthStats() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    
    monthStats.value = {
      'monthlyEarnings': _getMonthlyEarnings(startOfMonth),
      'newCustomers': _getNewCustomers(startOfMonth),
      'topProducts': _getTopProducts(startOfMonth),
      'growthRate': _getGrowthRate(startOfMonth),
    };
  }
}
```

### **2. ويدجت الإحصائيات السريعة:**

```dart
Widget _buildQuickStatsBar(AffiliateController controller) {
  return Container(
    padding: const EdgeInsets.all(16),
    margin: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [Colors.orange.shade400, Colors.orange.shade600],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(16),
    ),
    child: Obx(() => Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'نقرات اليوم',
            '${controller.todayStats['todayClicks'] ?? 0}',
            Icons.mouse,
            Colors.white,
          ),
        ),
        Expanded(
          child: _buildStatCard(
            'عمولات اليوم',
            '${controller.todayStats['todayEarnings']?.toStringAsFixed(2) ?? '0.00'} دج',
            Icons.monetization_on,
            Colors.white,
          ),
        ),
        Expanded(
          child: _buildStatCard(
            'مبيعات اليوم',
            '${controller.todayStats['todaySales'] ?? 0}',
            Icons.shopping_cart,
            Colors.white,
          ),
        ),
        Expanded(
          child: _buildStatCard(
            'معدل التحويل',
            '${controller.todayStats['todayConversionRate']?.toStringAsFixed(1) ?? '0.0'}%',
            Icons.trending_up,
            Colors.white,
          ),
        ),
      ],
    )),
  );
}
```

## 🔧 **الأولوية الثالثة: الأدوات السريعة**

### **1. قسم الأدوات السريعة:**

```dart
Widget _buildQuickActionsSection(AffiliateController controller) {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 16),
    child: Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأدوات السريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'إنشاء رابط',
                    Icons.add_link,
                    Colors.blue,
                    () => _showCreateLinkDialog(controller),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    'مشاركة منتج',
                    Icons.share,
                    Colors.green,
                    () => _showShareProductDialog(controller),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    'العمولات',
                    Icons.account_balance_wallet,
                    Colors.orange,
                    () => Get.toNamed('/affiliate-dashboard'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    'تقرير سريع',
                    Icons.analytics,
                    Colors.purple,
                    () => _generateQuickReport(controller),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}
```

## 📱 **الأولوية الرابعة: المنتجات المقترحة**

### **1. منتجات مخصصة للمسوق:**

```dart
Widget _buildRecommendedProducts(AffiliateController controller) {
  return Container(
    margin: const EdgeInsets.all(16),
    child: Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'منتجات مقترحة للتسويق',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () => Get.toNamed('/affiliate-dashboard'),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // فلاتر سريعة
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('عمولة عالية', true),
                  _buildFilterChip('جديد', false),
                  _buildFilterChip('رائج', false),
                  _buildFilterChip('عرض خاص', false),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // قائمة المنتجات
            Obx(() => SizedBox(
              height: 280,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: controller.recommendedProducts.length,
                itemBuilder: (context, index) {
                  final product = controller.recommendedProducts[index];
                  return Container(
                    width: 200,
                    margin: const EdgeInsets.only(right: 12),
                    child: AffiliateProductCard(
                      product: product,
                      onCreateLink: () => controller.createProductLink(product.id),
                      onShare: () => _shareProduct(product),
                    ),
                  );
                },
              ),
            )),
          ],
        ),
      ),
    ),
  );
}
```

## 📈 **الأولوية الخامسة: تطوير التقارير**

### **1. تحسين تبويب التقارير:**

```dart
Widget _buildReportsTab(AffiliateController controller) {
  return SingleChildScrollView(
    padding: const EdgeInsets.all(16),
    child: Column(
      children: [
        // فلاتر التقارير
        _buildReportFilters(controller),
        
        const SizedBox(height: 20),
        
        // الرسوم البيانية
        _buildPerformanceCharts(controller),
        
        const SizedBox(height: 20),
        
        // تقارير مفصلة
        _buildDetailedReports(controller),
      ],
    ),
  );
}

Widget _buildPerformanceCharts(AffiliateController controller) {
  return Card(
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'أداء آخر 30 يوم',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // رسم بياني للعمولات
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                // بيانات الرسم البياني
                lineBarsData: [
                  LineChartBarData(
                    spots: controller.getCommissionChartData(),
                    isCurved: true,
                    color: Colors.orange,
                    barWidth: 3,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
```

## 🔔 **الأولوية السادسة: نظام الإشعارات**

### **1. إضافة NotificationService للمسوق:**

```dart
class AffiliateNotificationService extends GetxService {
  // إشعار عند النقر على الرابط
  void onLinkClicked(String linkId, String productName) {
    Get.snackbar(
      '🖱️ نقرة جديدة',
      'تم النقر على رابط $productName',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.blue.withOpacity(0.8),
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }
  
  // إشعار عند البيع
  void onSaleCompleted(String productName, double commission) {
    Get.snackbar(
      '🎉 مبيعة جديدة!',
      'تم بيع $productName - عمولتك: ${commission.toStringAsFixed(2)} دج',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green.withOpacity(0.8),
      colorText: Colors.white,
      duration: const Duration(seconds: 5),
    );
  }
  
  // إشعار عند وصول الحد الأدنى للصرف
  void onPayoutThresholdReached(double amount) {
    Get.snackbar(
      '💰 جاهز للصرف',
      'يمكنك الآن طلب صرف ${amount.toStringAsFixed(2)} دج',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.orange.withOpacity(0.8),
      colorText: Colors.white,
      duration: const Duration(seconds: 5),
    );
  }
}
```

## 📋 **خطة التنفيذ العملية:**

### **الأسبوع الأول:**
1. ✅ إنشاء AffiliateHomeScreen
2. ✅ تحديث التوجيه للمسوق
3. ✅ إضافة الإحصائيات السريعة

### **الأسبوع الثاني:**
1. ✅ تطوير الأدوات السريعة
2. ✅ إنشاء AffiliateProductCard
3. ✅ تحسين قسم المنتجات المقترحة

### **الأسبوع الثالث:**
1. ✅ تطوير تبويب التقارير
2. ✅ إضافة الرسوم البيانية
3. ✅ تحسين فلاتر التقارير

### **الأسبوع الرابع:**
1. ✅ إضافة نظام الإشعارات
2. ✅ تحسين تجربة المستخدم
3. ✅ اختبار شامل للنظام

---
*هذا الدليل يوفر خطة عملية مفصلة لتطوير نظام مسوق متكامل ومتقدم.*
