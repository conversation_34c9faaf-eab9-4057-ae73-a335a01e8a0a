import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Custom mouse event handler to prevent mouse tracker assertion errors
/// This wrapper helps manage mouse events more reliably on desktop platforms
class SafeMouseRegion extends StatefulWidget {
  final Widget child;
  final void Function(PointerEnterEvent)? onEnter;
  final void Function(PointerExitEvent)? onExit;
  final void Function(PointerHoverEvent)? onHover;
  final MouseCursor cursor;
  final bool opaque;

  const SafeMouseRegion({
    super.key,
    required this.child,
    this.onEnter,
    this.onExit,
    this.onHover,
    this.cursor = MouseCursor.defer,
    this.opaque = true,
  });

  @override
  State<SafeMouseRegion> createState() => _SafeMouseRegionState();
}

class _SafeMouseRegionState extends State<SafeMouseRegion> {
  DateTime? _lastHoverEvent;
  bool _isHovered = false;
  static const Duration _hoverThrottle = Duration(milliseconds: 16); // ~60fps

  @override
  Widget build(BuildContext context) {
    // Wrap in a container with explicit size to prevent "no size" errors
    return Container(
      constraints: const BoxConstraints(minWidth: 0, minHeight: 0),
      child: MouseRegion(
        cursor: widget.cursor,
        opaque: widget.opaque,
        onEnter: _handleEnter,
        onExit: _handleExit,
        onHover: _handleHover,
        child: widget.child,
      ),
    );
  }

  void _handleEnter(PointerEnterEvent event) {
    if (!mounted) return;

    try {
      // Use post frame callback to avoid assertion errors during layout
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isHovered = true;
          });
          widget.onEnter?.call(event);
        }
      });
    } catch (e) {
      // Silently handle any assertion errors
      if (kDebugMode) {
        debugPrint('SafeMouseRegion: Enter event error handled: $e');
      }
    }
  }

  void _handleExit(PointerExitEvent event) {
    if (!mounted) return;

    try {
      // Use post frame callback to avoid assertion errors during layout
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isHovered = false;
            _lastHoverEvent = null;
          });
          widget.onExit?.call(event);
        }
      });
    } catch (e) {
      // Silently handle any assertion errors
      if (kDebugMode) {
        debugPrint('SafeMouseRegion: Exit event error handled: $e');
      }
    }
  }

  void _handleHover(PointerHoverEvent event) {
    if (!mounted) return;

    try {
      final now = DateTime.now();
      if (_lastHoverEvent != null &&
          now.difference(_lastHoverEvent!) < _hoverThrottle) {
        return; // Throttle hover events
      }

      // Use post frame callback to avoid assertion errors during layout
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _lastHoverEvent = now;
          widget.onHover?.call(event);
        }
      });
    } catch (e) {
      // Silently handle any assertion errors
      if (kDebugMode) {
        debugPrint('SafeMouseRegion: Hover event error handled: $e');
      }
    }
  }
}

/// Safe gesture detector that handles mouse events more reliably
class SafeGestureDetector extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final void Function(TapDownDetails)? onTapDown;
  final void Function(TapUpDetails)? onTapUp;
  final HitTestBehavior? behavior;

  const SafeGestureDetector({
    super.key,
    required this.child,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onTapDown,
    this.onTapUp,
    this.behavior,
  });

  @override
  State<SafeGestureDetector> createState() => _SafeGestureDetectorState();
}

class _SafeGestureDetectorState extends State<SafeGestureDetector> {
  DateTime? _lastTapTime;
  static const Duration _tapThrottle = Duration(milliseconds: 100);

  @override
  Widget build(BuildContext context) {
    // Wrap in a container with explicit constraints to prevent sizing issues
    return Container(
      constraints: const BoxConstraints(minWidth: 0, minHeight: 0),
      child: GestureDetector(
        behavior: widget.behavior ?? HitTestBehavior.opaque,
        onTap: _handleTap,
        onDoubleTap: widget.onDoubleTap,
        onLongPress: widget.onLongPress,
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        child: widget.child,
      ),
    );
  }

  void _handleTap() {
    if (!mounted) return;

    try {
      final now = DateTime.now();
      if (_lastTapTime != null &&
          now.difference(_lastTapTime!) < _tapThrottle) {
        return; // Prevent rapid taps
      }

      // Use post frame callback to avoid assertion errors during layout
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _lastTapTime = now;
          widget.onTap?.call();
        }
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('SafeGestureDetector: Tap event error handled: $e');
      }
    }
  }

  void _handleTapDown(TapDownDetails details) {
    if (!mounted) return;

    try {
      // Use post frame callback to avoid assertion errors during layout
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onTapDown?.call(details);
        }
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('SafeGestureDetector: TapDown event error handled: $e');
      }
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!mounted) return;

    try {
      // Use post frame callback to avoid assertion errors during layout
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onTapUp?.call(details);
        }
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('SafeGestureDetector: TapUp event error handled: $e');
      }
    }
  }
}

/// Utility class for desktop-specific mouse handling
class DesktopMouseUtils {
  static bool get isDesktop {
    return Theme.of(navigatorKey.currentContext!).platform == TargetPlatform.windows ||
           Theme.of(navigatorKey.currentContext!).platform == TargetPlatform.macOS ||
           Theme.of(navigatorKey.currentContext!).platform == TargetPlatform.linux;
  }

  static Widget wrapWithSafeMouseHandling(Widget child) {
    if (!isDesktop) return child;

    return SafeMouseRegion(
      child: child,
    );
  }
}

// Global navigator key for accessing context
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

/// Global error handler for mouse tracker assertions
class MouseTrackerErrorHandler {
  static bool _initialized = false;
  static DateTime? _lastErrorTime;
  static int _suppressedErrorCount = 0;
  static const Duration _suppressionDuration = Duration(seconds: 5);

  static void initialize() {
    if (_initialized) return;

    // Override Flutter's error handling for mouse tracker assertions
    FlutterError.onError = (FlutterErrorDetails details) {
      final exception = details.exception;

      // Check if this is a mouse tracker assertion error
      if (exception is AssertionError &&
          exception.toString().contains('_debugDuringDeviceUpdate')) {
        _handleMouseTrackerError();
        return;
      }

      // Check for render box sizing errors
      if (exception.toString().contains('Cannot hit test a render box with no size') ||
          exception.toString().contains('RenderBox was not laid out') ||
          exception.toString().contains('RenderFlex overflowed') ||
          exception.toString().contains('BoxConstraints forces an infinite') ||
          exception.toString().contains('A RenderFlex overflowed')) {
        if (kDebugMode) {
          debugPrint('Render box sizing error handled (suppressed in production)');
        }
        return;
      }

      // Check for other mouse tracker related errors
      if (exception.toString().contains('mouse_tracker.dart') ||
          exception.toString().contains('MouseTracker')) {
        _handleMouseTrackerError();
        return;
      }

      // For other errors, use the default handler
      FlutterError.presentError(details);
    };

    _initialized = true;
  }

  static void _handleMouseTrackerError() {
    final now = DateTime.now();

    // Suppress repeated errors within the suppression duration
    if (_lastErrorTime != null &&
        now.difference(_lastErrorTime!) < _suppressionDuration) {
      _suppressedErrorCount++;
      return;
    }

    // Print error message with suppression info if applicable
    if (kDebugMode) {
      if (_suppressedErrorCount > 0) {
        debugPrint('Mouse tracker assertion handled (${_suppressedErrorCount + 1} occurrences suppressed)');
        _suppressedErrorCount = 0;
      } else {
        debugPrint('Mouse tracker assertion handled (further similar errors will be suppressed for ${_suppressionDuration.inSeconds}s)');
      }
    }

    _lastErrorTime = now;
  }

  static void reset() {
    _lastErrorTime = null;
    _suppressedErrorCount = 0;
  }
}

/// Widget wrapper that provides safe mouse interaction handling
class SafeInteractionWrapper extends StatelessWidget {
  final Widget child;
  final bool enableMouseTracking;

  const SafeInteractionWrapper({
    super.key,
    required this.child,
    this.enableMouseTracking = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!enableMouseTracking) {
      return child;
    }

    return Container(
      constraints: const BoxConstraints(minWidth: 0, minHeight: 0),
      child: RepaintBoundary(
        child: child,
      ),
    );
  }
}
