import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/category.dart';

import '../../controllers/category_controller.dart';
import '../../widgets/product_card.dart';

class CategoryScreen extends StatelessWidget {
  final Category category;
  final SubCategory? subCategory;

  const CategoryScreen({
    super.key,
    required this.category,
    this.subCategory,
  });

  @override
  Widget build(BuildContext context) {
    final CategoryController controller = Get.put(CategoryController());

    // تحميل المنتجات للفئة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadCategoryProducts(category.id, subCategory?.id);
    });

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(subCategory?.name ?? category.name),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context, controller),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context, controller),
          ),
        ],
      ),
      body: Column(
        children: [
          // بانر الفئة
          _buildCategoryBanner(),

          // شريط الفلاتر السريعة
          _buildQuickFilters(controller),

          // عدد المنتجات وخيارات الترتيب
          _buildProductsHeader(controller),

          // قائمة المنتجات
          Expanded(
            child: Obx(() => controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : controller.filteredProducts.isEmpty
                    ? _buildEmptyState()
                    : _buildProductsGrid(controller)),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryBanner() {
    final String imageUrl;
    final String icon;
    final String name;
    final String description;

    if (subCategory != null) {
      imageUrl = subCategory!.image ??
          'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=300';
      icon = subCategory!.icon;
      name = subCategory!.name;
      description = subCategory!.description;
    } else {
      imageUrl = category.image;
      icon = category.icon;
      name = category.name;
      description = category.description;
    }

    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: NetworkImage(imageUrl),
          fit: BoxFit.cover,
        ),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.7),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    icon,
                    style: const TextStyle(fontSize: 32),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          description,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickFilters(CategoryController controller) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.white,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('الكل', true, () => controller.clearFilters()),
          const SizedBox(width: 8),
          _buildFilterChip(
              'الأكثر مبيعاً', false, () => controller.filterByPopular()),
          const SizedBox(width: 8),
          _buildFilterChip('الجديد', false, () => controller.filterByNew()),
          const SizedBox(width: 8),
          _buildFilterChip('عروض', false, () => controller.filterByOffers()),
          const SizedBox(width: 8),
          _buildFilterChip(
              'رجالي', false, () => controller.filterByGender('men')),
          const SizedBox(width: 8),
          _buildFilterChip(
              'نسائي', false, () => controller.filterByGender('women')),
          const SizedBox(width: 8),
          _buildFilterChip(
              'أطفال', false, () => controller.filterByGender('kids')),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.teal : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.teal : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildProductsHeader(CategoryController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Obx(() => Text(
                '${controller.filteredProducts.length} منتج',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              )),
          Row(
            children: [
              const Text('ترتيب حسب: '),
              Obx(() => DropdownButton<String>(
                    value: controller.sortBy.value,
                    underline: const SizedBox(),
                    items: const [
                      DropdownMenuItem(
                          value: 'default', child: Text('الافتراضي')),
                      DropdownMenuItem(
                          value: 'price_low', child: Text('السعر: من الأقل')),
                      DropdownMenuItem(
                          value: 'price_high', child: Text('السعر: من الأعلى')),
                      DropdownMenuItem(value: 'name', child: Text('الاسم')),
                      DropdownMenuItem(value: 'rating', child: Text('التقييم')),
                      DropdownMenuItem(value: 'newest', child: Text('الأحدث')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        controller.sortProducts(value);
                      }
                    },
                  )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductsGrid(CategoryController controller) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _getCrossAxisCount(),
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: controller.filteredProducts.length,
        itemBuilder: (context, index) {
          final product = controller.filteredProducts[index];
          return ProductCard(product: product);
        },
      ),
    );
  }

  int _getCrossAxisCount() {
    final width = Get.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات في هذه الفئة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفلاتر أو البحث عن منتجات أخرى',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('العودة للرئيسية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context, CategoryController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في المنتجات'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'ابحث عن منتج...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) => controller.searchProducts(value),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context, CategoryController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية المنتجات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('نطاق السعر:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Obx(() => RangeSlider(
                    values: RangeValues(
                      controller.minPrice.value,
                      controller.maxPrice.value,
                    ),
                    min: 0,
                    max: 1000,
                    divisions: 20,
                    labels: RangeLabels(
                      '\$${controller.minPrice.value.round()}',
                      '\$${controller.maxPrice.value.round()}',
                    ),
                    onChanged: (values) {
                      controller.minPrice.value = values.start;
                      controller.maxPrice.value = values.end;
                    },
                  )),
              const SizedBox(height: 16),
              const Text('الجنس:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              Obx(() => Column(
                    children: [
                      CheckboxListTile(
                        title: const Text('رجالي'),
                        value: controller.selectedGenders.contains('men'),
                        onChanged: (value) => controller.toggleGender('men'),
                      ),
                      CheckboxListTile(
                        title: const Text('نسائي'),
                        value: controller.selectedGenders.contains('women'),
                        onChanged: (value) => controller.toggleGender('women'),
                      ),
                      CheckboxListTile(
                        title: const Text('أطفال'),
                        value: controller.selectedGenders.contains('kids'),
                        onChanged: (value) => controller.toggleGender('kids'),
                      ),
                      CheckboxListTile(
                        title: const Text('للجميع'),
                        value: controller.selectedGenders.contains('unisex'),
                        onChanged: (value) => controller.toggleGender('unisex'),
                      ),
                    ],
                  )),
              const SizedBox(height: 16),
              const Text('المميزات:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              Obx(() => Column(
                    children: [
                      CheckboxListTile(
                        title: const Text('حماية UV'),
                        value: controller.hasUVProtection.value,
                        onChanged: (value) =>
                            controller.hasUVProtection.value = value ?? false,
                      ),
                      CheckboxListTile(
                        title: const Text('مضاد للوهج'),
                        value: controller.isPolarized.value,
                        onChanged: (value) =>
                            controller.isPolarized.value = value ?? false,
                      ),
                      CheckboxListTile(
                        title: const Text('حماية من الأشعة الزرقاء'),
                        value: controller.hasBlueLight.value,
                        onChanged: (value) =>
                            controller.hasBlueLight.value = value ?? false,
                      ),
                    ],
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              controller.clearFilters();
              Get.back();
            },
            child: const Text('مسح الفلاتر'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.applyFilters();
              Get.back();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}
