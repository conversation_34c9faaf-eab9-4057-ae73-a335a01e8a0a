# 🔥 إعداد Firebase للتطبيق

## المشكلة الحالية
التطبيق يواجه مشكلة في الاتصال بـ Firestore بسبب إعدادات Firebase غير مكتملة.

## الحل السريع

### 1. إعداد Firebase Console

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر المشروع: `my-ecommerce-app-61373`
3. إذا لم يكن موجوداً، أنشئ مشروع جديد بنفس الاسم

### 2. إعداد Firestore Database

1. في Firebase Console، اذهب إلى **Firestore Database**
2. اضغط **Create database**
3. اختر **Start in test mode** (مؤقتاً للاختبار)
4. اختر المنطقة الأقرب لك

### 3. إعداد قواعد Firestore

في **Firestore Database > Rules**، استبدل القواعد بهذا:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة للجميع (للاختبار فقط)
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

**⚠️ هذه القواعد للاختبار فقط! يجب تقييدها لاحقاً للأمان.**

### 4. إعداد Authentication

1. اذهب إلى **Authentication**
2. اضغط **Get started**
3. في تبويب **Sign-in method**
4. فعّل **Email/Password**

### 5. إعداد Web App

1. في **Project Overview**، اضغط على أيقونة الويب `</>`
2. أدخل اسم التطبيق: `my-ecommerce-web`
3. فعّل **Firebase Hosting** (اختياري)
4. اضغط **Register app**
5. انسخ **Web App ID** من الكود المعروض

### 6. تحديث الكود

استبدل في `lib/firebase_options.dart`:

```dart
// السطر 36
appId: '1:805685165017:web:YOUR_ACTUAL_WEB_APP_ID_HERE',
```

استبدل في `web/index.html`:

```javascript
// السطر 49
appId: "1:805685165017:web:YOUR_ACTUAL_WEB_APP_ID_HERE"
```

## اختبار الإعداد

1. احفظ التغييرات
2. في Terminal، اضغط `r` لإعادة تحميل التطبيق
3. اضغط على زر "اختبار قاعدة البيانات"
4. يجب أن تنجح جميع الاختبارات

## إعدادات الأمان (مهم للإنتاج)

بعد التأكد من عمل التطبيق، غيّر قواعد Firestore إلى:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح للمستخدمين المصادق عليهم فقط
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // بيانات المستخدمين - كل مستخدم يصل لبياناته فقط
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // المنتجات - قراءة للجميع، كتابة للإدمن فقط
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // بيانات الاختبار - للاختبار فقط
    match /test/{testId} {
      allow read, write: if true;
    }
  }
}
```

## استكشاف الأخطاء

### خطأ "permission-denied"
- تحقق من قواعد Firestore
- تأكد من تسجيل دخول المستخدم إذا كانت القواعد تتطلب ذلك

### خطأ "unavailable"
- تحقق من اتصال الإنترنت
- تأكد من صحة إعدادات Firebase

### خطأ "timeout"
- تحقق من Web App ID
- تأكد من تفعيل Firestore في Firebase Console

## ملاحظات مهمة

1. **لا تشارك API Keys** في الكود المفتوح المصدر
2. **استخدم Environment Variables** للإنتاج
3. **فعّل Firebase Security Rules** قبل النشر
4. **راقب استخدام Firebase** لتجنب التكاليف الزائدة

## الدعم

إذا واجهت مشاكل:
1. تحقق من [Firebase Documentation](https://firebase.google.com/docs)
2. راجع [Flutter Fire Documentation](https://firebase.flutter.dev/)
3. تحقق من Console في المتصفح للأخطاء التفصيلية
