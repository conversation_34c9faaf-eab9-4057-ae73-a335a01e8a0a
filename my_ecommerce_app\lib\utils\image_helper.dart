import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:math' as math;

class ImageHelper {
  // بناء صورة المنتج مع معالجة الأخطاء
  static Widget buildProductImage({
    required String imageUrl,
    required double width,
    required double height,
    String? fallbackType,
    BorderRadius? borderRadius,
    BoxFit fit = BoxFit.cover,
  }) {
    // التحقق من صحة الأبعاد
    if (!width.isFinite || width <= 0) width = 150.0;
    if (!height.isFinite || height <= 0) height = 150.0;

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => _buildLoadingPlaceholder(width, height),
        errorWidget: (context, url, error) => buildColoredPlaceholder(
          width: width,
          height: height,
          text: fallbackType ?? 'منتج',
          backgroundColor: _getRandomColor(),
          borderRadius: borderRadius,
        ),
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 100),
      ),
    );
  }

  // بناء placeholder ملون آمن
  static Widget buildColoredPlaceholder({
    required double width,
    required double height,
    required String text,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    // التحقق من صحة الأبعاد
    if (!width.isFinite || width <= 0) width = 150.0;
    if (!height.isFinite || height <= 0) height = 150.0;

    // حساب حجم الخط بناءً على الأبعاد (آمن)
    final double baseFontSize = math.min(width, height) * 0.12;
    final double fontSize = math.max(12.0, math.min(baseFontSize, 24.0));

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? _getRandomColor(),
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            backgroundColor ?? _getRandomColor(),
            (backgroundColor ?? _getRandomColor()).withOpacity(0.8),
          ],
        ),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: fontSize, // حجم خط آمن ومحدود
              fontWeight: FontWeight.bold,
              shadows: const [
                Shadow(
                  offset: Offset(1, 1),
                  blurRadius: 2,
                  color: Colors.black26,
                ),
              ],
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  // بناء placeholder التحميل
  static Widget _buildLoadingPlaceholder(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: SizedBox(
          width: math.min(width * 0.3, 30),
          height: math.min(height * 0.3, 30),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Colors.teal.shade300,
            ),
          ),
        ),
      ),
    );
  }

  // الحصول على لون عشوائي آمن
  static Color _getRandomColor() {
    final colors = [
      Colors.teal.shade400,
      Colors.blue.shade400,
      Colors.purple.shade400,
      Colors.orange.shade400,
      Colors.green.shade400,
      Colors.indigo.shade400,
      Colors.pink.shade400,
      Colors.amber.shade400,
    ];
    return colors[math.Random().nextInt(colors.length)];
  }

  // بناء صورة آمنة مع معالجة شاملة للأخطاء
  static Widget buildSafeImage({
    required String imageUrl,
    double? width,
    double? height,
    String fallbackText = 'صورة',
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    // قيم افتراضية آمنة
    final safeWidth = (width?.isFinite == true && width! > 0) ? width! : 150.0;
    final safeHeight = (height?.isFinite == true && height! > 0) ? height! : 150.0;

    if (imageUrl.isEmpty || !_isValidUrl(imageUrl)) {
      return buildColoredPlaceholder(
        width: safeWidth,
        height: safeHeight,
        text: fallbackText,
        backgroundColor: _getRandomColor(),
        borderRadius: borderRadius,
      );
    }

    return buildProductImage(
      imageUrl: imageUrl,
      width: safeWidth,
      height: safeHeight,
      fallbackType: fallbackText,
      borderRadius: borderRadius,
      fit: fit,
    );
  }

  // التحقق من صحة الرابط
  static bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  // بناء صورة دائرية آمنة
  static Widget buildCircularImage({
    required String imageUrl,
    required double radius,
    String fallbackText = 'صورة',
  }) {
    final safeRadius = (radius.isFinite && radius > 0) ? radius : 25.0;
    
    return CircleAvatar(
      radius: safeRadius,
      backgroundColor: _getRandomColor(),
      child: ClipOval(
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          width: safeRadius * 2,
          height: safeRadius * 2,
          fit: BoxFit.cover,
          placeholder: (context, url) => Icon(
            Icons.person,
            size: safeRadius,
            color: Colors.white,
          ),
          errorWidget: (context, url, error) => Text(
            fallbackText.isNotEmpty ? fallbackText[0].toUpperCase() : '؟',
            style: TextStyle(
              color: Colors.white,
              fontSize: math.max(12.0, safeRadius * 0.6),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
