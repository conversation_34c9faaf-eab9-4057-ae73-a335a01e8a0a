import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Enhanced layout error handler that prevents render box sizing errors
class LayoutErrorHandler {
  static bool _initialized = false;
  static int _suppressedLayoutErrors = 0;
  static DateTime? _lastLayoutErrorTime;
  static const Duration _suppressionDuration = Duration(seconds: 5);

  /// Initialize the layout error handler
  static void initialize() {
    if (_initialized) return;

    // Override the default error widget builder
    ErrorWidget.builder = (FlutterErrorDetails details) {
      if (_isLayoutError(details.exception)) {
        _handleLayoutError();
        return _buildSafeErrorWidget(details);
      }
      
      // For non-layout errors, use default behavior in debug mode
      if (kDebugMode) {
        return ErrorWidget(details.exception);
      }
      
      // In production, show a minimal error widget
      return _buildSafeErrorWidget(details);
    };

    _initialized = true;
  }

  /// Check if the error is a layout-related error
  static bool _isLayoutError(Object exception) {
    final errorString = exception.toString();
    return errorString.contains('RenderBox') ||
           errorString.contains('BoxConstraints') ||
           errorString.contains('RenderFlex overflowed') ||
           errorString.contains('Cannot hit test a render box with no size') ||
           errorString.contains('RenderObject was not laid out') ||
           errorString.contains('A RenderFlex overflowed') ||
           errorString.contains('BoxConstraints forces an infinite');
  }

  /// Handle layout errors with suppression
  static void _handleLayoutError() {
    final now = DateTime.now();

    // Suppress repeated errors within the suppression duration
    if (_lastLayoutErrorTime != null &&
        now.difference(_lastLayoutErrorTime!) < _suppressionDuration) {
      _suppressedLayoutErrors++;
      return;
    }

    // Print error message with suppression info if applicable
    if (kDebugMode) {
      if (_suppressedLayoutErrors > 0) {
        debugPrint('Layout error handled (${_suppressedLayoutErrors + 1} occurrences suppressed)');
        _suppressedLayoutErrors = 0;
      } else {
        debugPrint('Layout error handled (further similar errors will be suppressed for ${_suppressionDuration.inSeconds}s)');
      }
    }

    _lastLayoutErrorTime = now;
  }

  /// Build a safe error widget that won't cause additional layout issues
  static Widget _buildSafeErrorWidget(FlutterErrorDetails details) {
    return Container(
      constraints: const BoxConstraints(
        minWidth: 0,
        minHeight: 0,
        maxWidth: double.infinity,
        maxHeight: double.infinity,
      ),
      color: kDebugMode ? Colors.red.withValues(alpha: 0.1) : Colors.transparent,
      child: kDebugMode
          ? Center(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'Layout Error\n(Check Debug Console)',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  /// Reset error counters
  static void reset() {
    _lastLayoutErrorTime = null;
    _suppressedLayoutErrors = 0;
  }
}

/// Safe container widget that prevents common layout issues
class SafeLayoutContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BoxConstraints? constraints;
  final Decoration? decoration;
  final double? width;
  final double? height;

  const SafeLayoutContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.constraints,
    this.decoration,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      decoration: decoration,
      constraints: constraints ?? const BoxConstraints(
        minWidth: 0,
        minHeight: 0,
      ),
      child: RepaintBoundary(
        child: child,
      ),
    );
  }
}

/// Safe column widget that prevents overflow issues
class SafeColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const SafeColumn({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.minHeight,
              maxHeight: constraints.maxHeight.isFinite 
                  ? constraints.maxHeight 
                  : MediaQuery.of(context).size.height,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: mainAxisAlignment,
                crossAxisAlignment: crossAxisAlignment,
                mainAxisSize: mainAxisSize,
                children: children,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Safe row widget that prevents overflow issues
class SafeRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const SafeRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: constraints.minWidth,
              maxWidth: constraints.maxWidth.isFinite 
                  ? constraints.maxWidth 
                  : MediaQuery.of(context).size.width,
            ),
            child: IntrinsicWidth(
              child: Row(
                mainAxisAlignment: mainAxisAlignment,
                crossAxisAlignment: crossAxisAlignment,
                mainAxisSize: mainAxisSize,
                children: children,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Safe flexible widget that prevents sizing issues
class SafeFlexible extends StatelessWidget {
  final Widget child;
  final int flex;
  final FlexFit fit;

  const SafeFlexible({
    super.key,
    required this.child,
    this.flex = 1,
    this.fit = FlexFit.loose,
  });

  @override
  Widget build(BuildContext context) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 0,
          minHeight: 0,
        ),
        child: child,
      ),
    );
  }
}
