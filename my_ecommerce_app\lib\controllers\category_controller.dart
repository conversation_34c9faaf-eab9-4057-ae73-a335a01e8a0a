import 'package:get/get.dart';
import '../models/product.dart';

class CategoryController extends GetxController {
  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList<Product> allProducts = <Product>[].obs;
  final RxList<Product> filteredProducts = <Product>[].obs;
  final RxString searchQuery = ''.obs;
  final RxString sortBy = 'default'.obs;

  // Filter variables
  final RxDouble minPrice = 0.0.obs;
  final RxDouble maxPrice = 1000.0.obs;
  final RxList<String> selectedGenders = <String>[].obs;
  final RxBool hasUVProtection = false.obs;
  final RxBool isPolarized = false.obs;
  final RxBool hasBlueLight = false.obs;
  final RxString selectedCategory = ''.obs;
  final RxString selectedSubCategory = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // تحديث المنتجات المفلترة عند تغيير أي فلتر
    ever(searchQuery, (_) => _applyFiltersAndSort());
    ever(sortBy, (_) => _applyFiltersAndSort());
    ever(minPrice, (_) => _applyFiltersAndSort());
    ever(maxPrice, (_) => _applyFiltersAndSort());
    ever(selectedGenders, (_) => _applyFiltersAndSort());
    ever(hasUVProtection, (_) => _applyFiltersAndSort());
    ever(isPolarized, (_) => _applyFiltersAndSort());
    ever(hasBlueLight, (_) => _applyFiltersAndSort());
  }

  // تحميل منتجات الفئة
  Future<void> loadCategoryProducts(String categoryId,
      [String? subCategoryId]) async {
    isLoading.value = true;
    selectedCategory.value = categoryId;
    selectedSubCategory.value = subCategoryId ?? '';

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(milliseconds: 500));

      // تحميل جميع المنتجات أولاً
      allProducts.value = Product.demoProducts;

      // فلترة المنتجات حسب الفئة والفئة الفرعية
      _filterByCategory();
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء تحميل المنتجات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // فلترة المنتجات حسب الفئة
  void _filterByCategory() {
    List<Product> categoryProducts = allProducts.where((product) {
      // فلترة حسب الفئة الرئيسية
      bool matchesCategory =
          _productMatchesCategory(product, selectedCategory.value);

      // فلترة حسب الفئة الفرعية إذا كانت محددة
      if (selectedSubCategory.value.isNotEmpty) {
        matchesCategory = matchesCategory &&
            _productMatchesSubCategory(product, selectedSubCategory.value);
      }

      return matchesCategory;
    }).toList();

    allProducts.value = categoryProducts;
    _applyFiltersAndSort();
  }

  // التحقق من تطابق المنتج مع الفئة
  bool _productMatchesCategory(Product product, String categoryId) {
    switch (categoryId) {
      case 'glasses':
        return product.type == EyewearType.sunglasses ||
            product.type == EyewearType.prescription ||
            product.type == EyewearType.reading ||
            product.type == EyewearType.fashion;
      case 'accessories':
        // يمكن إضافة منطق للإكسسوارات لاحقاً
        return false;
      case 'lenses':
        // يمكن إضافة منطق للعدسات لاحقاً
        return false;
      case 'services':
        // الخدمات لا تحتوي على منتجات فعلية
        return false;
      case 'content':
        // المحتوى التوعوي لا يحتوي على منتجات
        return false;
      default:
        return true;
    }
  }

  // التحقق من تطابق المنتج مع الفئة الفرعية
  bool _productMatchesSubCategory(Product product, String subCategoryId) {
    switch (subCategoryId) {
      // نظارات طبية
      case 'prescription':
      case 'prescription_men':
      case 'prescription_women':
      case 'prescription_kids':
      case 'frames_only':
        return product.type == EyewearType.prescription;

      // نظارات شمسية
      case 'sunglasses':
      case 'sunglasses_men':
      case 'sunglasses_women':
      case 'sunglasses_kids':
      case 'sports_sunglasses':
      case 'polarized':
        return product.type == EyewearType.sunglasses;

      // أنواع أخرى
      case 'reading':
        return product.type == EyewearType.reading;
      case 'computer':
        return product.specs.lensType == LensType.blueLight;
      case 'custom_prescription':
        return product.type == EyewearType.prescription;

      default:
        return true;
    }
  }

  // تطبيق الفلاتر والترتيب
  void _applyFiltersAndSort() {
    List<Product> filtered = List.from(allProducts);

    // فلتر البحث
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered
          .where((product) =>
              product.name
                  .toLowerCase()
                  .contains(searchQuery.value.toLowerCase()) ||
              product.description
                  .toLowerCase()
                  .contains(searchQuery.value.toLowerCase()))
          .toList();
    }

    // فلتر السعر
    filtered = filtered
        .where((product) =>
            product.price >= minPrice.value && product.price <= maxPrice.value)
        .toList();

    // فلتر الجنس
    if (selectedGenders.isNotEmpty) {
      filtered = filtered
          .where((product) => selectedGenders
              .contains(product.targetGender.toString().split('.').last))
          .toList();
    }

    // فلتر المميزات
    if (hasUVProtection.value) {
      filtered = filtered
          .where((product) => product.specs.hasUVProtection == true)
          .toList();
    }

    if (isPolarized.value) {
      filtered = filtered
          .where((product) => product.specs.lensType == LensType.polarized)
          .toList();
    }

    if (hasBlueLight.value) {
      filtered = filtered
          .where((product) => product.specs.lensType == LensType.blueLight)
          .toList();
    }

    // ترتيب المنتجات
    _sortFilteredProducts(filtered);

    filteredProducts.value = filtered;
  }

  // ترتيب المنتجات
  void _sortFilteredProducts(List<Product> products) {
    switch (sortBy.value) {
      case 'price_low':
        products.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_high':
        products.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'name':
        products.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'rating':
        products.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'newest':
        products.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      default:
        // الترتيب الافتراضي (حسب الشعبية)
        products.sort((a, b) => b.isPopular ? 1 : -1);
        break;
    }
  }

  // البحث في المنتجات
  void searchProducts(String query) {
    searchQuery.value = query;
  }

  // ترتيب المنتجات
  void sortProducts(String sortType) {
    sortBy.value = sortType;
  }

  // فلترة سريعة
  void filterByPopular() {
    clearFilters();
    List<Product> popular =
        allProducts.where((product) => product.isPopular).toList();
    filteredProducts.value = popular;
  }

  void filterByNew() {
    clearFilters();
    sortBy.value = 'newest';
  }

  void filterByOffers() {
    clearFilters();
    List<Product> offers =
        allProducts.where((product) => product.discountPercentage > 0).toList();
    filteredProducts.value = offers;
  }

  void filterByGender(String gender) {
    selectedGenders.clear();
    selectedGenders.add(gender);
  }

  // تبديل الجنس في الفلاتر
  void toggleGender(String gender) {
    if (selectedGenders.contains(gender)) {
      selectedGenders.remove(gender);
    } else {
      selectedGenders.add(gender);
    }
  }

  // تطبيق الفلاتر
  void applyFilters() {
    _applyFiltersAndSort();
  }

  // مسح جميع الفلاتر
  void clearFilters() {
    searchQuery.value = '';
    sortBy.value = 'default';
    minPrice.value = 0.0;
    maxPrice.value = 1000.0;
    selectedGenders.clear();
    hasUVProtection.value = false;
    isPolarized.value = false;
    hasBlueLight.value = false;
    _applyFiltersAndSort();
  }

  // الحصول على عدد المنتجات في الفئة
  int getProductCount(String categoryId, [String? subCategoryId]) {
    return Product.demoProducts.where((product) {
      bool matches = _productMatchesCategory(product, categoryId);
      if (subCategoryId != null && subCategoryId.isNotEmpty) {
        matches = matches && _productMatchesSubCategory(product, subCategoryId);
      }
      return matches;
    }).length;
  }

  // الحصول على المنتجات المميزة للفئة
  List<Product> getFeaturedProducts(String categoryId, [int limit = 4]) {
    return Product.demoProducts
        .where((product) => _productMatchesCategory(product, categoryId))
        .where((product) => product.isRecommended || product.isPopular)
        .take(limit)
        .toList();
  }
}
