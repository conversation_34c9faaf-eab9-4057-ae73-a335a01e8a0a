import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// إعدادات التطبيق العامة
class AppConfig {
  // إعدادات Mouse Tracker
  static const bool enableMouseTrackerErrorSuppression = true;
  static const Duration mouseTrackerErrorSuppressionDuration = Duration(seconds: 5);
  static const bool enableMouseEventThrottling = true;
  static const Duration mouseEventThrottleDuration = Duration(milliseconds: 16);

  // إعدادات الأداء
  static const bool enableRepaintBoundaries = true;
  static const bool enableDebugPainting = false;
  static const bool enablePerformanceOverlay = false;

  // إعدادات التطوير
  static const bool enableDebugLogs = kDebugMode;
  static const bool enableVerboseLogs = false;
  static const bool enableAppTester = kDebugMode;

  // إعدادات Firebase
  static const Duration firebaseTimeout = Duration(seconds: 10);
  static const bool enableFirebaseEmulator = false;
  static const String firebaseEmulatorHost = 'localhost';

  // إعدادات UI
  static const bool enableAnimations = true;
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const bool enableHapticFeedback = true;

  /// تحقق من كون المنصة سطح مكتب
  static bool get isDesktop {
    return defaultTargetPlatform == TargetPlatform.windows ||
           defaultTargetPlatform == TargetPlatform.macOS ||
           defaultTargetPlatform == TargetPlatform.linux;
  }

  /// تحقق من كون المنصة موبايل
  static bool get isMobile {
    return defaultTargetPlatform == TargetPlatform.android ||
           defaultTargetPlatform == TargetPlatform.iOS;
  }

  /// تحقق من كون المنصة ويب
  static bool get isWeb {
    return kIsWeb;
  }

  /// طباعة معلومات التطبيق
  static void printAppInfo() {
    if (!enableDebugLogs) return;

    debugPrint('=== معلومات التطبيق ===');
    debugPrint('المنصة: ${defaultTargetPlatform.name}');
    debugPrint('سطح المكتب: $isDesktop');
    debugPrint('الموبايل: $isMobile');
    debugPrint('الويب: $isWeb');
    debugPrint('وضع التطوير: $kDebugMode');
    debugPrint('وضع الإنتاج: $kReleaseMode');
    debugPrint('وضع الملف الشخصي: $kProfileMode');
    debugPrint('========================');
  }

  /// إعدادات الثيم حسب المنصة
  static ThemeData getThemeForPlatform() {
    return ThemeData(
      primarySwatch: Colors.teal,
      useMaterial3: true,
      fontFamily: 'Arial',
      textTheme: const TextTheme(
        bodyLarge: TextStyle(fontSize: 16),
        bodyMedium: TextStyle(fontSize: 14),
      ),
      // تحسينات خاصة بسطح المكتب
      scrollbarTheme: isDesktop ? const ScrollbarThemeData(
        thumbVisibility: WidgetStatePropertyAll(true),
        trackVisibility: WidgetStatePropertyAll(true),
      ) : null,
    );
  }
}
