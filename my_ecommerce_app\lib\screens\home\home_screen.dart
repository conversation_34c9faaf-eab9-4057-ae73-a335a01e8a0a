import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/user.dart';
import '../../widgets/user_role_widgets.dart';
import '../../widgets/home_widgets.dart';
import '../../widgets/search_widgets.dart';
import '../../widgets/secret_admin_access.dart';
import '../../services/auth_service.dart';
import '../../controllers/home_controller.dart';
import '../../utils/web_scroll_fix.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة الـ controller
    final HomeController homeController = Get.put(HomeController());
    return Scaffold(
      appBar: const UserRoleAppBar(title: "متجر النظارات العصري"),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.extended(
            onPressed: () => homeController.resetDemoData(),
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة تعيين البيانات'),
            heroTag: "reset_data",
          ),
          const SizedBox(height: 8),
          FloatingActionButton.extended(
            onPressed: () => Get.toNamed('/database-test'),
            backgroundColor: Colors.teal,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.storage),
            label: const Text('اختبار قاعدة البيانات'),
            heroTag: "test_db",
          ),
        ],
      ),
      body: Stack(
        children: [
          // الزر المخفي للإدمن
          const HiddenAdminButton(),

          Obx(() {
            if (homeController.isLoading.value) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'جاري تحميل المنتجات...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              );
            }

            if (homeController.hasError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      homeController.errorMessage.value,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.red,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => homeController.refreshData(),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () => homeController.refreshData(),
              child: WebScrollFix.createOptimizedScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    // شريط الإعلانات المتحرك
                    const AnnouncementBanner(),

                    // شريط البحث السريع
                    const QuickSearchBar(),

                    // رسالة ترحيب للمسوقين
                    Obx(() {
                      final authService = Get.find<AuthService>();
                      final user = authService.currentUser;

                      if (user != null && user.role == UserRole.affiliate) {
                        return Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          margin: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.orange.shade400,
                                Colors.orange.shade600
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.trending_up,
                                  color: Colors.white, size: 30),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'مرحباً ${user.firstName}! 👋',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const Text(
                                      'انقر على أي منتج لإنشاء رابط تسويقي وكسب العمولات',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              TextButton(
                                onPressed: () => Get.toNamed('/settings'),
                                style: TextButton.styleFrom(
                                  backgroundColor:
                                      Colors.white.withValues(alpha: 0.2),
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('الإعدادات'),
                              ),
                            ],
                          ),
                        );
                      }
                      return const SizedBox();
                    }),

                    // بانر الترويج الرئيسي
                    const HeroBanner(),

                    // قسم الفئات
                    const CategoriesSection(),

                    // قسم العروض والخصومات
                    const OffersSection(),

                    // قسم المنتجات المميزة
                    const FeaturedProductsSection(),

                    // قسم الأكثر مبيعاً
                    const BestSellersSection(),

                    // قسم المنتجات الموصى بها
                    const RecommendedProductsSection(),

                    // قسم العلامات التجارية
                    const BrandsSection(),

                    // قسم المحتوى التوعوي
                    const EducationalContentSection(),

                    // قسم آراء العملاء
                    const TestimonialsSection(),

                    // قسم المزايا
                    const FeaturesSection(),

                    // فوتر
                    _buildFooter(),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFooterColumn('المتجر', [
                'من نحن',
                'اتصل بنا',
                'الأسئلة الشائعة',
              ]),
              _buildFooterColumn('الخدمات', [
                'الشحن والتوصيل',
                'الإرجاع والاستبدال',
                'الضمان',
              ]),
              _buildFooterColumn('الحساب', [
                'تسجيل الدخول',
                'إنشاء حساب',
                'طلباتي',
              ]),
            ],
          ),
          const SizedBox(height: 24),
          const Divider(color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            '© 2024 متجر النظارات العصري - جميع الحقوق محفوظة',
            style: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'صنع بـ ❤️ في الجزائر',
            style: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFooterColumn(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: GestureDetector(
                onTap: () =>
                    Get.snackbar('قريباً', 'هذه الميزة ستكون متاحة قريباً'),
                child: Text(
                  item,
                  style: TextStyle(
                    color: Colors.grey.shade400,
                    fontSize: 14,
                  ),
                ),
              ),
            )),
      ],
    );
  }
}
