import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/notification_service.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final notificationService = Get.find<NotificationService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('التنبيهات'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          Obx(() => notificationService.unreadCount.value > 0
              ? TextButton(
                  onPressed: () => notificationService.markAllAsRead(),
                  child: const Text(
                    'تحديد الكل كمقروء',
                    style: TextStyle(color: Colors.white),
                  ),
                )
              : const SizedBox()),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'clear_all':
                  _showClearAllDialog(context, notificationService);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف جميع التنبيهات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Obx(() {
        if (notificationService.notifications.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => notificationService.refreshNotifications(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: notificationService.notifications.length,
            itemBuilder: (context, index) {
              final notification = notificationService.notifications[index];
              return _buildNotificationCard(notification, notificationService);
            },
          ),
        );
      }),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد تنبيهات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ستظهر التنبيهات الجديدة هنا',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(
    NotificationModel notification,
    NotificationService service,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: notification.isRead ? 1 : 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: notification.isRead ? Colors.transparent : Colors.teal,
          width: notification.isRead ? 0 : 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          if (!notification.isRead) {
            service.markAsRead(notification.id);
          }
          _handleNotificationTap(notification);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة التنبيه
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getNotificationIcon(notification),
                  color: _getNotificationColor(notification),
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // محتوى التنبيه
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: notification.isRead
                                  ? FontWeight.normal
                                  : FontWeight.bold,
                              color: notification.isRead
                                  ? Colors.grey[700]
                                  : Colors.black,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.teal,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.body,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatTime(notification.timestamp),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),

                        // أزرار الإجراءات
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (!notification.isRead)
                              TextButton(
                                onPressed: () =>
                                    service.markAsRead(notification.id),
                                child: const Text(
                                  'تحديد كمقروء',
                                  style: TextStyle(fontSize: 12),
                                ),
                              ),
                            IconButton(
                              onPressed: () => _showDeleteDialog(
                                Get.context!,
                                notification,
                                service,
                              ),
                              icon: const Icon(Icons.delete_outline),
                              iconSize: 18,
                              color: Colors.grey[500],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getNotificationColor(NotificationModel notification) {
    final type = notification.data['type'];
    switch (type) {
      case 'order':
        return Colors.blue;
      case 'product':
        return Colors.green;
      case 'offer':
        return Colors.orange;
      case 'system':
        return Colors.purple;
      default:
        return Colors.teal;
    }
  }

  IconData _getNotificationIcon(NotificationModel notification) {
    final type = notification.data['type'];
    switch (type) {
      case 'order':
        return Icons.shopping_bag;
      case 'product':
        return Icons.inventory;
      case 'offer':
        return Icons.local_offer;
      case 'system':
        return Icons.settings;
      default:
        return Icons.notifications;
    }
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  void _handleNotificationTap(NotificationModel notification) {
    final type = notification.data['type'];
    final id = notification.data['id'];

    switch (type) {
      case 'order':
        Get.toNamed('/order-details', arguments: id);
        break;
      case 'product':
        Get.toNamed('/product-details', arguments: id);
        break;
      case 'offer':
        Get.toNamed('/offers');
        break;
      default:
        // عرض تفاصيل التنبيه
        Get.dialog(
          AlertDialog(
            title: Text(notification.title),
            content: Text(notification.body),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('موافق'),
              ),
            ],
          ),
        );
    }
  }

  void _showDeleteDialog(
    BuildContext context,
    NotificationModel notification,
    NotificationService service,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التنبيه'),
        content: const Text('هل تريد حذف هذا التنبيه؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              service.deleteNotification(notification.id);
              Navigator.of(context).pop();
            },
            child: const Text(
              'حذف',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(
    BuildContext context,
    NotificationService service,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع التنبيهات'),
        content: const Text(
            'هل تريد حذف جميع التنبيهات؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              service.clearAllNotifications();
              Navigator.of(context).pop();
            },
            child: const Text(
              'حذف الكل',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
