rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function isAffiliate() {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'affiliate' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // قواعد المستخدمين
    match /users/{userId} {
      // المستخدم يمكنه قراءة وتعديل بياناته فقط، أو الأدمن يمكنه قراءة جميع المستخدمين
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isOwner(userId) || isAdmin();
    }
    
    // قواعد المنتجات
    match /products/{productId} {
      // الجميع يمكنهم قراءة المنتجات
      allow read: if true;
      
      // فقط الأدمن يمكنه إضافة/تعديل/حذف المنتجات
      allow write: if isAdmin();
    }
    
    // قواعد الطلبات
    match /orders/{orderId} {
      // المستخدم يمكنه قراءة طلباته فقط، أو الأدمن يمكنه قراءة جميع الطلبات
      allow read: if isAuthenticated() && 
        (request.auth.uid == resource.data.userId || isAdmin());
      
      // المستخدم يمكنه إنشاء طلبات جديدة
      allow create: if isAuthenticated() && 
        request.auth.uid == request.resource.data.userId;
      
      // فقط الأدمن يمكنه تحديث حالة الطلبات
      allow update: if isAdmin();
      
      // فقط الأدمن يمكنه حذف الطلبات
      allow delete: if isAdmin();
    }
    
    // قواعد السلة
    match /carts/{userId} {
      // المستخدم يمكنه قراءة وتعديل سلته فقط
      allow read, write: if isOwner(userId);
    }
    
    // قواعد المفضلة
    match /wishlists/{userId} {
      // المستخدم يمكنه قراءة وتعديل مفضلته فقط
      allow read, write: if isOwner(userId);
    }
    
    // قواعد التنبيهات
    match /notifications/{notificationId} {
      // المستخدم يمكنه قراءة تنبيهاته فقط
      allow read: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
      
      // فقط الأدمن يمكنه إنشاء التنبيهات
      allow create: if isAdmin();
      
      // المستخدم يمكنه تحديث حالة قراءة تنبيهاته فقط
      allow update: if isAuthenticated() && 
        request.auth.uid == resource.data.userId &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isRead']);
      
      // فقط الأدمن يمكنه حذف التنبيهات
      allow delete: if isAdmin();
    }
    
    // قواعد طلبات المسوقين
    match /affiliate_requests/{requestId} {
      // المستخدم يمكنه قراءة طلبه فقط، أو الأدمن يمكنه قراءة جميع الطلبات
      allow read: if isAuthenticated() && 
        (request.auth.uid == resource.data.userId || isAdmin());
      
      // المستخدم يمكنه إنشاء طلب انضمام
      allow create: if isAuthenticated() && 
        request.auth.uid == request.resource.data.userId;
      
      // فقط الأدمن يمكنه تحديث حالة الطلبات
      allow update: if isAdmin();
      
      // فقط الأدمن يمكنه حذف الطلبات
      allow delete: if isAdmin();
    }
    
    // قواعد الروابط التسويقية
    match /affiliate_links/{linkId} {
      // المسوق يمكنه قراءة روابطه فقط، أو الأدمن يمكنه قراءة جميع الروابط
      allow read: if isAuthenticated() && 
        (request.auth.uid == resource.data.affiliateId || isAdmin());
      
      // المسوق يمكنه إنشاء روابط جديدة
      allow create: if isAffiliate() && 
        request.auth.uid == request.resource.data.affiliateId;
      
      // المسوق يمكنه تحديث روابطه فقط، أو الأدمن يمكنه تحديث جميع الروابط
      allow update: if isAuthenticated() && 
        (request.auth.uid == resource.data.affiliateId || isAdmin());
      
      // فقط الأدمن يمكنه حذف الروابط
      allow delete: if isAdmin();
    }
    
    // قواعد إحصائيات المسوقين
    match /affiliate_stats/{affiliateId} {
      // المسوق يمكنه قراءة إحصائياته فقط، أو الأدمن يمكنه قراءة جميع الإحصائيات
      allow read: if isOwner(affiliateId) || isAdmin();
      
      // فقط النظام (الأدمن) يمكنه تحديث الإحصائيات
      allow write: if isAdmin();
    }
    
    // قواعد بيانات الاختبار (للتطوير فقط)
    match /test/{document=**} {
      allow read, write: if isAuthenticated();
    }
    
    // قواعد التنبيهات المعلقة (للنظام)
    match /pending_notifications/{document=**} {
      allow read, write: if isAdmin();
    }
    
    // قواعد عامة - رفض الوصول لأي مجموعة غير محددة
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
