enum LinkType { product, category, general }
enum LinkStatus { active, inactive, expired }

class AffiliateLink {
  final String id;
  final String affiliateId;
  final String affiliateCode;
  final LinkType type;
  final String? productId; // للروابط الخاصة بمنتج معين
  final String? categoryId; // للروابط الخاصة بفئة معينة
  final String originalUrl; // الرابط الأصلي
  final String shortUrl; // الرابط المختصر
  final String trackingCode; // كود التتبع الفريد
  final LinkStatus status;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final int clickCount; // عدد النقرات
  final int conversionCount; // عدد التحويلات (المبيعات)
  final double totalEarnings; // إجمالي الأرباح من هذا الرابط
  final Map<String, dynamic> metadata; // بيانات إضافية

  AffiliateLink({
    required this.id,
    required this.affiliateId,
    required this.affiliateCode,
    required this.type,
    this.productId,
    this.categoryId,
    required this.originalUrl,
    required this.shortUrl,
    required this.trackingCode,
    this.status = LinkStatus.active,
    required this.createdAt,
    this.expiresAt,
    this.clickCount = 0,
    this.conversionCount = 0,
    this.totalEarnings = 0.0,
    this.metadata = const {},
  });

  // معدل التحويل
  double get conversionRate => clickCount > 0 ? (conversionCount / clickCount) * 100 : 0.0;
  
  // متوسط العمولة لكل نقرة
  double get earningsPerClick => clickCount > 0 ? totalEarnings / clickCount : 0.0;
  
  // متوسط العمولة لكل تحويل
  double get earningsPerConversion => conversionCount > 0 ? totalEarnings / conversionCount : 0.0;
  
  // هل الرابط منتهي الصلاحية
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
  
  // هل الرابط نشط
  bool get isActive => status == LinkStatus.active && !isExpired;

  // إنشاء رابط تسويقي كامل
  String getFullTrackingUrl(String baseUrl) {
    return '$baseUrl?ref=$affiliateCode&track=$trackingCode';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'affiliateId': affiliateId,
      'affiliateCode': affiliateCode,
      'type': type.toString(),
      'productId': productId,
      'categoryId': categoryId,
      'originalUrl': originalUrl,
      'shortUrl': shortUrl,
      'trackingCode': trackingCode,
      'status': status.toString(),
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'clickCount': clickCount,
      'conversionCount': conversionCount,
      'totalEarnings': totalEarnings,
      'metadata': metadata,
    };
  }

  factory AffiliateLink.fromJson(Map<String, dynamic> json) {
    return AffiliateLink(
      id: json['id'] ?? '',
      affiliateId: json['affiliateId'] ?? '',
      affiliateCode: json['affiliateCode'] ?? '',
      type: LinkType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => LinkType.general,
      ),
      productId: json['productId'],
      categoryId: json['categoryId'],
      originalUrl: json['originalUrl'] ?? '',
      shortUrl: json['shortUrl'] ?? '',
      trackingCode: json['trackingCode'] ?? '',
      status: LinkStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => LinkStatus.active,
      ),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      clickCount: json['clickCount'] ?? 0,
      conversionCount: json['conversionCount'] ?? 0,
      totalEarnings: (json['totalEarnings'] ?? 0.0).toDouble(),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  AffiliateLink copyWith({
    LinkStatus? status,
    int? clickCount,
    int? conversionCount,
    double? totalEarnings,
    Map<String, dynamic>? metadata,
  }) {
    return AffiliateLink(
      id: id,
      affiliateId: affiliateId,
      affiliateCode: affiliateCode,
      type: type,
      productId: productId,
      categoryId: categoryId,
      originalUrl: originalUrl,
      shortUrl: shortUrl,
      trackingCode: trackingCode,
      status: status ?? this.status,
      createdAt: createdAt,
      expiresAt: expiresAt,
      clickCount: clickCount ?? this.clickCount,
      conversionCount: conversionCount ?? this.conversionCount,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      metadata: metadata ?? this.metadata,
    );
  }
}

// نموذج تتبع النقرات
class ClickTracking {
  final String id;
  final String linkId;
  final String trackingCode;
  final String? customerId;
  final String ipAddress;
  final String userAgent;
  final String? referrer;
  final DateTime clickedAt;
  final bool converted; // هل تم التحويل (الشراء)
  final String? orderId; // معرف الطلب إذا تم التحويل
  final double? commissionAmount; // مبلغ العمولة إذا تم التحويل

  ClickTracking({
    required this.id,
    required this.linkId,
    required this.trackingCode,
    this.customerId,
    required this.ipAddress,
    required this.userAgent,
    this.referrer,
    required this.clickedAt,
    this.converted = false,
    this.orderId,
    this.commissionAmount,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'linkId': linkId,
      'trackingCode': trackingCode,
      'customerId': customerId,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'referrer': referrer,
      'clickedAt': clickedAt.toIso8601String(),
      'converted': converted,
      'orderId': orderId,
      'commissionAmount': commissionAmount,
    };
  }

  factory ClickTracking.fromJson(Map<String, dynamic> json) {
    return ClickTracking(
      id: json['id'] ?? '',
      linkId: json['linkId'] ?? '',
      trackingCode: json['trackingCode'] ?? '',
      customerId: json['customerId'],
      ipAddress: json['ipAddress'] ?? '',
      userAgent: json['userAgent'] ?? '',
      referrer: json['referrer'],
      clickedAt: DateTime.parse(json['clickedAt'] ?? DateTime.now().toIso8601String()),
      converted: json['converted'] ?? false,
      orderId: json['orderId'],
      commissionAmount: json['commissionAmount'] != null 
          ? (json['commissionAmount']).toDouble() 
          : null,
    );
  }
}
