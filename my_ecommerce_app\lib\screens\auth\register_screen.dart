import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../models/user.dart';

class RegisterScreen extends StatelessWidget {
  const RegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find<AuthController>();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'إنشاء حساب جديد',
          style: TextStyle(color: Colors.black87),
        ),
      ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: authController.registerForm<PERSON><PERSON>,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),
                
                // نوع الحساب
                const Text(
                  'نوع الحساب',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                Obx(() => Column(
                  children: UserRole.values.where((role) => role != UserRole.admin).map((role) {
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: RadioListTile<UserRole>(
                        title: Text(authController.getUserRoleText(role)),
                        subtitle: Text(
                          authController.getUserRoleDescription(role),
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        value: role,
                        groupValue: authController.selectedRole.value,
                        onChanged: (UserRole? value) {
                          if (value != null) {
                            authController.changeUserRole(value);
                          }
                        },
                        activeColor: Colors.teal,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        tileColor: Colors.white,
                      ),
                    );
                  }).toList(),
                )),
                
                const SizedBox(height: 24),
                
                // الاسم الأول
                TextFormField(
                  controller: authController.firstNameController,
                  validator: authController.validateName,
                  decoration: InputDecoration(
                    labelText: 'الاسم الأول',
                    prefixIcon: const Icon(Icons.person_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // الاسم الأخير
                TextFormField(
                  controller: authController.lastNameController,
                  validator: authController.validateName,
                  decoration: InputDecoration(
                    labelText: 'الاسم الأخير',
                    prefixIcon: const Icon(Icons.person_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // البريد الإلكتروني
                TextFormField(
                  controller: authController.emailController,
                  keyboardType: TextInputType.emailAddress,
                  validator: authController.validateEmail,
                  decoration: InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // رقم الهاتف
                TextFormField(
                  controller: authController.phoneController,
                  keyboardType: TextInputType.phone,
                  validator: authController.validatePhone,
                  decoration: InputDecoration(
                    labelText: 'رقم الهاتف',
                    prefixIcon: const Icon(Icons.phone_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // كلمة المرور
                Obx(() => TextFormField(
                  controller: authController.passwordController,
                  obscureText: !authController.isPasswordVisible.value,
                  validator: authController.validatePassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور',
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      icon: Icon(
                        authController.isPasswordVisible.value
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: authController.togglePasswordVisibility,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                )),
                
                const SizedBox(height: 16),
                
                // تأكيد كلمة المرور
                Obx(() => TextFormField(
                  controller: authController.confirmPasswordController,
                  obscureText: !authController.isConfirmPasswordVisible.value,
                  validator: authController.validateConfirmPassword,
                  decoration: InputDecoration(
                    labelText: 'تأكيد كلمة المرور',
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      icon: Icon(
                        authController.isConfirmPasswordVisible.value
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: authController.toggleConfirmPasswordVisibility,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                )),
                
                const SizedBox(height: 16),
                
                // كود الإحالة (اختياري)
                TextFormField(
                  controller: authController.referralCodeController,
                  validator: authController.validateReferralCode,
                  decoration: InputDecoration(
                    labelText: 'كود الإحالة (اختياري)',
                    prefixIcon: const Icon(Icons.card_giftcard_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    helperText: 'إذا كان لديك كود إحالة من مسوق',
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // زر إنشاء الحساب
                Obx(() => SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: authController.isLoading
                        ? null
                        : authController.register,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: authController.isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            'إنشاء الحساب',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                )),
                
                const SizedBox(height: 24),
                
                // رابط تسجيل الدخول
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('لديك حساب بالفعل؟ '),
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text(
                        'سجل دخولك',
                        style: TextStyle(
                          color: Colors.teal,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
