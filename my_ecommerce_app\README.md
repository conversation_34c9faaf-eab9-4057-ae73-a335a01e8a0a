# متجر النظارات العصري - My Ecommerce App

تطبيق تجارة إلكترونية متكامل مبني بـ Flutter يدعم عدة منصات (Android, iOS, Web, Desktop) مع Firebase كقاعدة بيانات.

## 🚀 الميزات

- 🔐 نظام مصادقة متكامل (تسجيل دخول، إنشاء حساب، إعادة تعيين كلمة المرور)
- 🛒 سلة تسوق ذكية
- ❤️ قائمة المفضلة
- 👥 نظام المسوقين بالعمولة
- 🔧 لوحة تحكم الإدارة
- 📱 تصميم متجاوب لجميع الأجهزة
- 🌐 دعم اللغة العربية

## 🛠️ التقنيات المستخدمة

- **Flutter** 3.19.3+ - إطار العمل الأساسي
- **Firebase** - قاعدة البيانات والمصادقة
  - Firebase Auth - المصادقة
  - Cloud Firestore - قاعدة البيانات
  - Firebase Core - الإعدادات الأساسية
- **GetX** - إدارة الحالة والتوجيه
- **SharedPreferences** - التخزين المحلي

## 📋 متطلبات النظام

- Flutter SDK 3.19.0+
- Dart SDK 3.3.1+
- Android Studio أو VS Code
- حساب Firebase مع مشروع مُعد

## ⚙️ إعداد المشروع

### 1. تثبيت المتطلبات

```bash
# تثبيت المكتبات
flutter pub get

# التحقق من إعداد Flutter
flutter doctor
```

### 2. إعداد Firebase

#### أ. إنشاء مشروع Firebase:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم المشروع الحالي: `my-ecommerce-app-61373`
3. فعّل Authentication و Firestore Database

#### ب. إعداد التطبيقات:
1. **للأندرويد**: ملف `google-services.json` موجود بالفعل
2. **للويب**: يجب الحصول على Web App ID من Firebase Console
3. **لـ iOS**: يجب إضافة `GoogleService-Info.plist`

#### ج. تحديث إعدادات Firebase:
في ملف `lib/firebase_options.dart`، استبدل القيم التالية بالقيم الحقيقية من Firebase Console:

```dart
// للويب
appId: '1:805685165017:web:YOUR_ACTUAL_WEB_APP_ID'

// لـ iOS
appId: '1:805685165017:ios:YOUR_ACTUAL_IOS_APP_ID'

// لـ macOS
appId: '1:805685165017:ios:YOUR_ACTUAL_MACOS_APP_ID'

// لـ Windows
appId: '1:805685165017:web:YOUR_ACTUAL_WINDOWS_APP_ID'
```

في ملف `web/index.html`، استبدل:
```javascript
appId: "1:805685165017:web:YOUR_ACTUAL_WEB_APP_ID"
```

### 3. إعداد قواعد Firestore

في Firebase Console > Firestore Database > Rules، استخدم القواعد التالية:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح للمستخدمين المصادق عليهم بالقراءة والكتابة
    match /{document=**} {
      allow read, write: if request.auth != null;
    }

    // السماح بقراءة المنتجات للجميع
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null &&
        (resource.data.role == 'admin' || resource.data.role == 'affiliate');
    }

    // بيانات الاختبار
    match /test/{testId} {
      allow read, write: if true;
    }
  }
}
```

## 🧪 اختبار الاتصال بقاعدة البيانات

التطبيق يحتوي على أداة اختبار مدمجة:

1. شغّل التطبيق: `flutter run`
2. اضغط على زر "اختبار قاعدة البيانات" في الصفحة الرئيسية
3. اضغط "تشغيل اختبارات قاعدة البيانات"
4. ستظهر نتائج الاختبارات:
   - ✅ اختبار الاتصال بـ Firestore
   - ✅ اختبار كتابة البيانات
   - ✅ اختبار قراءة البيانات
   - ✅ اختبار Firebase Authentication

## 🚀 تشغيل التطبيق

```bash
# للويب
flutter run -d chrome

# للأندرويد
flutter run -d android

# لسطح المكتب (Windows)
flutter run -d windows

# لبناء التطبيق للإنتاج
flutter build web
flutter build apk
flutter build windows
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بـ Firebase:**
   - تأكد من صحة إعدادات `firebase_options.dart`
   - تحقق من اتصال الإنترنت
   - تأكد من تفعيل Firestore في Firebase Console

2. **مشاكل في المصادقة:**
   - تأكد من تفعيل Authentication في Firebase Console
   - تحقق من إعداد Sign-in methods (Email/Password)

3. **مشاكل في البناء:**
   ```bash
   flutter clean
   flutter pub get
   flutter run
   ```

## 📁 هيكل المشروع

```
lib/
├── config/          # إعدادات التطبيق
├── controllers/     # تحكم GetX
├── models/          # نماذج البيانات
├── routes/          # توجيه الصفحات
├── screens/         # شاشات التطبيق
├── services/        # خدمات قاعدة البيانات
├── utils/           # أدوات مساعدة
├── widgets/         # مكونات قابلة للإعادة
└── main.dart        # نقطة البداية
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.
