import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/affiliate_service.dart';
import '../services/affiliate_database_service.dart';
import '../models/affiliate_request.dart';

class AffiliateDatabaseTestScreen extends StatefulWidget {
  const AffiliateDatabaseTestScreen({super.key});

  @override
  State<AffiliateDatabaseTestScreen> createState() =>
      _AffiliateDatabaseTestScreenState();
}

class _AffiliateDatabaseTestScreenState
    extends State<AffiliateDatabaseTestScreen> {
  final AffiliateService _affiliateService = Get.find<AffiliateService>();
  final AffiliateDatabaseService _dbService = AffiliateDatabaseService();
  Map<String, bool>? _testResults;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار قاعدة بيانات المسوقين'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبار خدمات المسوقين',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('اختبار ربط خدمة المسوقين بـ Firestore'),
                    Text('- إدارة المسوقين'),
                    Text('- طلبات الانضمام'),
                    Text('- الروابط التسويقية'),
                    Text('- العمولات والإحصائيات'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isRunning ? null : _runTests,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isRunning
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('جاري تشغيل الاختبارات...'),
                      ],
                    )
                  : const Text(
                      'تشغيل اختبارات خدمة المسوقين',
                      style: TextStyle(fontSize: 16),
                    ),
            ),
            const SizedBox(height: 16),
            if (_testResults != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نتائج الاختبارات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ..._testResults!.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Icon(
                                entry.value ? Icons.check_circle : Icons.error,
                                color: entry.value ? Colors.green : Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _getTestDisplayName(entry.key),
                                  style: TextStyle(
                                    color:
                                        entry.value ? Colors.green : Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Text(
                                entry.value ? 'نجح' : 'فشل',
                                style: TextStyle(
                                  color:
                                      entry.value ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _allTestsPassed()
                              ? Colors.green.shade50
                              : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                _allTestsPassed() ? Colors.green : Colors.red,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _allTestsPassed()
                              ? '🎉 جميع الاختبارات نجحت! خدمة المسوقين مربوطة بقاعدة البيانات بشكل صحيح.'
                              : '⚠️ بعض الاختبارات فشلت. يرجى التحقق من إعدادات قاعدة البيانات.',
                          style: TextStyle(
                            color: _allTestsPassed()
                                ? Colors.green.shade800
                                : Colors.red.shade800,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _refreshAffiliateData,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('تحديث بيانات المسوقين'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _createTestRequest,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('إنشاء طلب تجريبي'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });

    try {
      final results = <String, bool>{};

      // اختبار جلب المسوقين
      debugPrint('🔄 اختبار جلب المسوقين...');
      try {
        final affiliates = await _dbService.getAllAffiliates();
        results['get_affiliates'] = true;
        debugPrint('✅ تم جلب ${affiliates.length} مسوق');
      } catch (e) {
        results['get_affiliates'] = false;
        debugPrint('❌ فشل جلب المسوقين: $e');
      }

      // اختبار البحث بالكود
      debugPrint('🔄 اختبار البحث بالكود...');
      try {
        final affiliate = await _dbService.getAffiliateByCode('TEST_CODE');
        results['search_by_code'] = true;
        debugPrint(
            '✅ البحث بالكود يعمل (النتيجة: ${affiliate != null ? 'موجود' : 'غير موجود'})');
      } catch (e) {
        results['search_by_code'] = false;
        debugPrint('❌ فشل البحث بالكود: $e');
      }

      // اختبار جلب الروابط التسويقية
      debugPrint('🔄 اختبار جلب الروابط التسويقية...');
      try {
        final links = await _dbService.getAffiliateLinks('test_user_id');
        results['get_affiliate_links'] = true;
        debugPrint('✅ تم جلب ${links.length} رابط تسويقي');
      } catch (e) {
        results['get_affiliate_links'] = false;
        debugPrint('❌ فشل جلب الروابط التسويقية: $e');
      }

      // اختبار جلب العمولات
      debugPrint('🔄 اختبار جلب العمولات...');
      try {
        final commissions =
            await _dbService.getAffiliateCommissions('test_user_id');
        results['get_commissions'] = true;
        debugPrint('✅ تم جلب ${commissions.length} عمولة');
      } catch (e) {
        results['get_commissions'] = false;
        debugPrint('❌ فشل جلب العمولات: $e');
      }

      // اختبار جلب طلبات الانضمام
      debugPrint('🔄 اختبار جلب طلبات الانضمام...');
      try {
        final requests =
            await _dbService.getUserAffiliateRequests('test_user_id');
        results['get_affiliate_requests'] = true;
        debugPrint('✅ تم جلب ${requests.length} طلب انضمام');
      } catch (e) {
        results['get_affiliate_requests'] = false;
        debugPrint('❌ فشل جلب طلبات الانضمام: $e');
      }

      setState(() {
        _testResults = results;
      });

      debugPrint('\n📊 نتائج اختبارات خدمة المسوقين:');
      results.forEach((test, result) {
        final icon = result ? '✅' : '❌';
        debugPrint('$icon $test: ${result ? 'نجح' : 'فشل'}');
      });

      final allPassed = results.values.every((result) => result);
      debugPrint(
          '\n${allPassed ? '🎉' : '⚠️'} النتيجة النهائية: ${allPassed ? 'جميع الاختبارات نجحت' : 'بعض الاختبارات فشلت'}');
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تشغيل الاختبارات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _refreshAffiliateData() async {
    try {
      await _affiliateService.loadAffiliatesFromDatabase();
      Get.snackbar(
        'تم',
        'تم تحديث بيانات المسوقين بنجاح',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث البيانات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _createTestRequest() async {
    try {
      final testRequest = AffiliateRequest(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'test_user_id',
        userFullName: 'مستخدم تجريبي',
        userEmail: '<EMAIL>',
        userPhone: '+213555000000',
        reason: 'طلب تجريبي لاختبار النظام',
        experience: 'خبرة تجريبية في التسويق',
        socialMediaLinks:
            'https://facebook.com/test, https://instagram.com/test',
        status: AffiliateRequestStatus.pending,
        requestDate: DateTime.now(),
      );

      final success = await _dbService.submitAffiliateRequest(testRequest);

      if (success) {
        Get.snackbar(
          'تم',
          'تم إنشاء طلب تجريبي بنجاح',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في إنشاء الطلب التجريبي',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  String _getTestDisplayName(String testKey) {
    switch (testKey) {
      case 'get_affiliates':
        return 'جلب المسوقين';
      case 'search_by_code':
        return 'البحث بالكود';
      case 'get_affiliate_links':
        return 'جلب الروابط التسويقية';
      case 'get_commissions':
        return 'جلب العمولات';
      case 'get_affiliate_requests':
        return 'جلب طلبات الانضمام';
      default:
        return testKey;
    }
  }

  bool _allTestsPassed() {
    return _testResults?.values.every((result) => result) ?? false;
  }
}
