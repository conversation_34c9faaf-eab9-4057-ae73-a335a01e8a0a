import 'package:cloud_firestore/cloud_firestore.dart';

/// طبقة عامة للتعامل مع Firestore بشكل مختصر ومنظم
class DatabaseService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  /// إضافة أو تحديث مستند
  Future<void> setDocument(String collection, String docId, Map<String, dynamic> data) async {
    await _db.collection(collection).doc(docId).set(data, SetOptions(merge: true));
  }

  /// جلب مستند
  Future<DocumentSnapshot<Map<String, dynamic>>> getDocument(String collection, String docId) async {
    return await _db.collection(collection).doc(docId).get();
  }

  /// حذف مستند
  Future<void> deleteDocument(String collection, String docId) async {
    await _db.collection(collection).doc(docId).delete();
  }

  /// جلب كل المستندات في مجموعة
  Future<List<Map<String, dynamic>>> getCollection(String collection) async {
    final query = await _db.collection(collection).get();
    return query.docs.map((doc) => doc.data()).toList();
  }

  /// إضافة عنصر إلى Array داخل مستند
  Future<void> addToArray(String collection, String docId, String arrayField, Map<String, dynamic> value) async {
    await _db.collection(collection).doc(docId).update({
      arrayField: FieldValue.arrayUnion([value])
    });
  }

  /// حذف عنصر من Array داخل مستند
  Future<void> removeFromArray(String collection, String docId, String arrayField, Map<String, dynamic> value) async {
    await _db.collection(collection).doc(docId).update({
      arrayField: FieldValue.arrayRemove([value])
    });
  }
}
