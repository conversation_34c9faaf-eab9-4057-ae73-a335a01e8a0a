import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/cart.dart';
import '../models/product.dart';
import '../services/cart_service.dart';
import '../services/auth_service.dart';

class CartController extends GetxController {
  final CartService _cartService = Get.find<CartService>();
  final AuthService _authService = Get.find<AuthService>();

  // Getters للوصول لبيانات السلة
  Cart? get cart => _cartService.cart;
  RxInt get itemCount => _cartService.itemCount;
  RxDouble get totalPrice => _cartService.totalPrice;

  // متغيرات للتحكم في واجهة المستخدم
  final RxBool isLoading = false.obs;
  final RxBool isProcessingCheckout = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeCart();
  }

  // تهيئة السلة
  Future<void> _initializeCart() async {
    final user = _authService.currentUser;
    if (user != null && cart == null) {
      await _cartService.createCart(user.id);
    }
  }

  // إضافة منتج للسلة
  Future<void> addToCart(Product product, {int quantity = 1}) async {
    final user = _authService.currentUser;

    if (user == null) {
      Get.snackbar(
        'تسجيل الدخول مطلوب',
        'يجب تسجيل الدخول أولاً لإضافة المنتجات للسلة',
        snackPosition: SnackPosition.TOP,
      );
      Get.toNamed('/login');
      return;
    }

    if (!user.isCustomer && !user.isAdmin) {
      Get.snackbar(
        'غير مسموح',
        'المسوقون لا يمكنهم إضافة منتجات للسلة',
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    isLoading.value = true;

    try {
      await _cartService.addToCart(product, quantity: quantity);
    } finally {
      isLoading.value = false;
    }
  }

  // تحديث كمية منتج
  Future<void> updateQuantity(int productId, int quantity) async {
    isLoading.value = true;

    try {
      await _cartService.updateQuantity(productId, quantity);
    } finally {
      isLoading.value = false;
    }
  }

  // زيادة كمية منتج
  Future<void> increaseQuantity(int productId) async {
    final currentQuantity = _cartService.getProductQuantity(productId);
    await updateQuantity(productId, currentQuantity + 1);
  }

  // تقليل كمية منتج
  Future<void> decreaseQuantity(int productId) async {
    final currentQuantity = _cartService.getProductQuantity(productId);
    if (currentQuantity > 1) {
      await updateQuantity(productId, currentQuantity - 1);
    } else {
      await removeFromCart(productId);
    }
  }

  // حذف منتج من السلة
  Future<void> removeFromCart(int productId) async {
    isLoading.value = true;

    try {
      await _cartService.removeFromCart(productId);
    } finally {
      isLoading.value = false;
    }
  }

  // تفريغ السلة
  Future<void> clearCart() async {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد التفريغ'),
        content: const Text('هل أنت متأكد من تفريغ السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              isLoading.value = true;

              try {
                await _cartService.clearCart();
              } finally {
                isLoading.value = false;
              }
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  // التحقق من وجود منتج في السلة
  bool hasProduct(int productId) {
    return _cartService.hasProduct(productId);
  }

  // الحصول على كمية منتج في السلة
  int getProductQuantity(int productId) {
    return _cartService.getProductQuantity(productId);
  }

  // حساب الإجمالي مع الضرائب والشحن
  double getTotalWithTaxAndShipping() {
    return _cartService.getTotalWithTaxAndShipping();
  }

  // الانتقال لصفحة الدفع
  Future<void> proceedToCheckout() async {
    if (cart == null || cart!.items.isEmpty) {
      Get.snackbar(
        'السلة فارغة',
        'أضف منتجات للسلة أولاً',
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    final user = _authService.currentUser;
    if (user == null) {
      Get.snackbar(
        'تسجيل الدخول مطلوب',
        'يجب تسجيل الدخول أولاً لإتمام الطلب',
        snackPosition: SnackPosition.TOP,
      );
      Get.toNamed('/login');
      return;
    }

    isProcessingCheckout.value = true;

    try {
      // هنا يمكن إضافة منطق الدفع الفعلي
      await Future.delayed(const Duration(seconds: 2)); // محاكاة معالجة الدفع

      Get.snackbar(
        'تم الطلب بنجاح',
        'سيتم التواصل معك قريباً لتأكيد الطلب والتوصيل (الدفع عند الاستلام)',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 4),
      );

      await _cartService.clearCart();
      Get.back(); // العودة للصفحة الرئيسية

    } catch (e) {
      Get.snackbar(
        'خطأ في الطلب',
        'حدث خطأ أثناء معالجة الطلب، يرجى المحاولة مرة أخرى',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isProcessingCheckout.value = false;
    }
  }

  // تنسيق السعر
  String formatPrice(double price) {
    return '${price.toStringAsFixed(0)} دج';
  }

  // الحصول على معلومات ملخص السلة
  Map<String, dynamic> getCartSummary() {
    final subtotal = cart?.totalPrice ?? 0.0;
    const taxRate = 0.19; // ضريبة الجزائر 19%
    const shippingCost = 500.0; // 500 دج
    const freeShippingThreshold = 15000.0; // 15000 دج للشحن المجاني

    final tax = subtotal * taxRate;
    final shipping = subtotal >= freeShippingThreshold ? 0.0 : shippingCost;
    final total = subtotal + tax + shipping;

    return {
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'total': total,
      'itemCount': cart?.totalItems ?? 0,
      'freeShipping': subtotal >= freeShippingThreshold,
      'paymentMethod': 'الدفع عند الاستلام', // طريقة الدفع الوحيدة
    };
  }
}
