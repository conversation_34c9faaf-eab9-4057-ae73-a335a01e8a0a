enum UserRole { customer, affiliate, admin }

enum UserStatus { active, inactive, suspended }

class User {
  final String id;
  final String email;
  final String password; // في التطبيق الحقيقي، لا تحفظ كلمة المرور مباشرة
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final UserRole role;
  final UserStatus status;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  
  // خاص بالمسوقين
  final String? affiliateCode; // كود المسوق الفريد
  final double totalEarnings; // إجمالي الأرباح
  final double pendingEarnings; // الأرباح المعلقة
  final int totalReferrals; // عدد العملاء المُحالين
  
  // خاص بالعملاء
  final String? referredBy; // كود المسوق الذي أحال العميل
  final double totalSpent; // إجمالي المبلغ المنفق
  final int totalOrders; // عدد الطلبات

  User({
    required this.id,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.profileImageUrl,
    required this.role,
    this.status = UserStatus.active,
    required this.createdAt,
    this.lastLoginAt,
    this.affiliateCode,
    this.totalEarnings = 0.0,
    this.pendingEarnings = 0.0,
    this.totalReferrals = 0,
    this.referredBy,
    this.totalSpent = 0.0,
    this.totalOrders = 0,
  });

  String get fullName => '$firstName $lastName';
  
  bool get isAffiliate => role == UserRole.affiliate;
  bool get isCustomer => role == UserRole.customer;
  bool get isAdmin => role == UserRole.admin;
  bool get isActive => status == UserStatus.active;

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'role': role.toString(),
      'status': status.toString(),
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'affiliateCode': affiliateCode,
      'totalEarnings': totalEarnings,
      'pendingEarnings': pendingEarnings,
      'totalReferrals': totalReferrals,
      'referredBy': referredBy,
      'totalSpent': totalSpent,
      'totalOrders': totalOrders,
    };
  }

  // إنشاء من JSON
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      password: '', // لا نحفظ كلمة المرور في JSON
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      phoneNumber: json['phoneNumber'],
      profileImageUrl: json['profileImageUrl'],
      role: UserRole.values.firstWhere(
        (e) => e.toString() == json['role'],
        orElse: () => UserRole.customer,
      ),
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => UserStatus.active,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
      affiliateCode: json['affiliateCode'],
      totalEarnings: (json['totalEarnings'] ?? 0.0).toDouble(),
      pendingEarnings: (json['pendingEarnings'] ?? 0.0).toDouble(),
      totalReferrals: json['totalReferrals'] ?? 0,
      referredBy: json['referredBy'],
      totalSpent: (json['totalSpent'] ?? 0.0).toDouble(),
      totalOrders: json['totalOrders'] ?? 0,
    );
  }

  // نسخ مع تعديل
  User copyWith({
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
    UserRole? role,
    UserStatus? status,
    DateTime? lastLoginAt,
    String? affiliateCode,
    double? totalEarnings,
    double? pendingEarnings,
    int? totalReferrals,
    String? referredBy,
    double? totalSpent,
    int? totalOrders,
  }) {
    return User(
      id: id,
      email: email ?? this.email,
      password: password,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      affiliateCode: affiliateCode ?? this.affiliateCode,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      pendingEarnings: pendingEarnings ?? this.pendingEarnings,
      totalReferrals: totalReferrals ?? this.totalReferrals,
      referredBy: referredBy ?? this.referredBy,
      totalSpent: totalSpent ?? this.totalSpent,
      totalOrders: totalOrders ?? this.totalOrders,
    );
  }
}
