import '../models/product.dart';

class ApiService {
  static const String baseUrl = 'https://api.example.com'; // Replace with your API URL

  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  // Get all products
  Future<List<Product>> getProducts() async {
    try {
      // For now, return demo products
      // In real app, you would make HTTP request:
      // final response = await http.get(Uri.parse('$baseUrl/products'));
      // if (response.statusCode == 200) {
      //   final List<dynamic> data = json.decode(response.body);
      //   return data.map((json) => Product.fromJson(json)).toList();
      // }

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      return Product.demoProducts;
    } catch (e) {
      throw Exception('Failed to load products: $e');
    }
  }

  // Get product by ID
  Future<Product?> getProductById(int id) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      final products = Product.demoProducts;
      return products.firstWhere(
        (product) => product.id == id,
        orElse: () => throw Exception('Product not found'),
      );
    } catch (e) {
      throw Exception('Failed to load product: $e');
    }
  }

  // Search products
  Future<List<Product>> searchProducts(String query) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));

      final products = Product.demoProducts;
      return products.where((product) =>
          product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase())
      ).toList();
    } catch (e) {
      throw Exception('Failed to search products: $e');
    }
  }
}