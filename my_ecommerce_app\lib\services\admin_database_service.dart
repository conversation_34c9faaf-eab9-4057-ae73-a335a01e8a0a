import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/product.dart';
import '../models/order.dart' as app_order;
import '../models/affiliate_request.dart';
import 'database_service.dart';

/// خدمة قاعدة البيانات الخاصة بالإدارة
class AdminDatabaseService {
  final DatabaseService _db = DatabaseService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== إدارة المستخدمين ====================

  /// جلب جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      return snapshot.docs.map((doc) => User.fromJson(doc.data())).toList();
    } catch (e) {
      debugPrint('خطأ في جلب المستخدمين: $e');
      return [];
    }
  }

  /// تحديث بيانات مستخدم
  Future<bool> updateUser(String userId, Map<String, dynamic> updates) async {
    try {
      await _db.setDocument('users', userId, updates);
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث المستخدم: $e');
      return false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(String userId) async {
    try {
      await _db.deleteDocument('users', userId);
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف المستخدم: $e');
      return false;
    }
  }

  // ==================== إدارة المنتجات ====================

  /// جلب جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    try {
      final snapshot = await _firestore.collection('products').get();
      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// إضافة منتج جديد
  Future<bool> addProduct(Product product) async {
    try {
      await _firestore.collection('products').add(product.toJson());
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تحديث منتج
  Future<bool> updateProduct(
      String productId, Map<String, dynamic> updates) async {
    try {
      await _firestore.collection('products').doc(productId).update(updates);
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث المنتج: $e');
      return false;
    }
  }

  /// حذف منتج
  Future<bool> deleteProduct(String productId) async {
    try {
      await _firestore.collection('products').doc(productId).delete();
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف المنتج: $e');
      return false;
    }
  }

  // ==================== إدارة الطلبات ====================

  /// جلب جميع الطلبات
  Future<List<app_order.Order>> getAllOrders() async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .orderBy('createdAt', descending: true)
          .get();
      return snapshot.docs
          .map((doc) => app_order.Order.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب الطلبات: $e');
      return <app_order.Order>[];
    }
  }

  /// تحديث حالة طلب
  Future<bool> updateOrderStatus(
      String orderId, Map<String, dynamic> updates) async {
    try {
      await _firestore.collection('orders').doc(orderId).update(updates);
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// جلب طلبات مستخدم معين
  Future<List<app_order.Order>> getUserOrders(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('customerId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();
      return snapshot.docs
          .map((doc) => app_order.Order.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب طلبات المستخدم: $e');
      return <app_order.Order>[];
    }
  }

  // ==================== إدارة طلبات المسوقين ====================

  /// جلب جميع طلبات المسوقين
  Future<List<AffiliateRequest>> getAllAffiliateRequests() async {
    try {
      final snapshot = await _firestore
          .collection('affiliate_requests')
          .orderBy('requestDate', descending: true)
          .get();
      return snapshot.docs
          .map(
              (doc) => AffiliateRequest.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب طلبات المسوقين: $e');
      return [];
    }
  }

  /// تحديث حالة طلب مسوق
  Future<bool> updateAffiliateRequest(
      String requestId, Map<String, dynamic> updates) async {
    try {
      await _firestore
          .collection('affiliate_requests')
          .doc(requestId)
          .update(updates);
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث طلب المسوق: $e');
      return false;
    }
  }

  /// إضافة طلب مسوق جديد
  Future<bool> addAffiliateRequest(AffiliateRequest request) async {
    try {
      await _firestore.collection('affiliate_requests').add(request.toJson());
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة طلب المسوق: $e');
      return false;
    }
  }

  // ==================== الإحصائيات ====================

  /// جلب إحصائيات عامة
  Future<Map<String, dynamic>> getGeneralStats() async {
    try {
      final usersCount = await _firestore.collection('users').count().get();
      final productsCount =
          await _firestore.collection('products').count().get();
      final ordersCount = await _firestore.collection('orders').count().get();
      final affiliateRequestsCount =
          await _firestore.collection('affiliate_requests').count().get();

      // حساب الإيرادات
      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('paymentStatus', isEqualTo: 'paid')
          .get();

      double totalRevenue = 0.0;
      for (var doc in ordersSnapshot.docs) {
        totalRevenue += (doc.data()['totalAmount'] as num?)?.toDouble() ?? 0.0;
      }

      return {
        'totalUsers': usersCount.count,
        'totalProducts': productsCount.count,
        'totalOrders': ordersCount.count,
        'totalAffiliateRequests': affiliateRequestsCount.count,
        'totalRevenue': totalRevenue,
      };
    } catch (e) {
      debugPrint('خطأ في جلب الإحصائيات: $e');
      return {};
    }
  }

  /// جلب إحصائيات شهرية
  Future<Map<String, dynamic>> getMonthlyStats() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);

      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('createdAt',
              isGreaterThanOrEqualTo: startOfMonth.toIso8601String())
          .get();

      double monthlyRevenue = 0.0;
      int monthlyOrders = ordersSnapshot.docs.length;

      for (var doc in ordersSnapshot.docs) {
        if (doc.data()['paymentStatus'] == 'paid') {
          monthlyRevenue +=
              (doc.data()['totalAmount'] as num?)?.toDouble() ?? 0.0;
        }
      }

      return {
        'monthlyOrders': monthlyOrders,
        'monthlyRevenue': monthlyRevenue,
      };
    } catch (e) {
      debugPrint('خطأ في جلب الإحصائيات الشهرية: $e');
      return {};
    }
  }

  // ==================== البحث والفلترة ====================

  /// البحث في المستخدمين
  Future<List<User>> searchUsers(String query) async {
    try {
      // البحث بالاسم أو البريد الإلكتروني
      final snapshot = await _firestore
          .collection('users')
          .where('email', isGreaterThanOrEqualTo: query.toLowerCase())
          .where('email', isLessThanOrEqualTo: '${query.toLowerCase()}\uf8ff')
          .get();

      return snapshot.docs.map((doc) => User.fromJson(doc.data())).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المستخدمين: $e');
      return [];
    }
  }

  /// البحث في المنتجات
  Future<List<Product>> searchProducts(String query) async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المنتجات: $e');
      return [];
    }
  }
}
