import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

/// إصلاحات خاصة بالتمرير في المتصفحات
class WebScrollFix {
  
  /// تطبيق إصلاحات التمرير على widget
  static Widget applyScrollFix(Widget child) {
    if (!kIsWeb) return child;
    
    return ScrollConfiguration(
      behavior: WebScrollBehaviorFixed(),
      child: child,
    );
  }

  /// إنشاء SingleChildScrollView محسن للويب
  static Widget createOptimizedScrollView({
    required Widget child,
    ScrollController? controller,
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    EdgeInsetsGeometry? padding,
    bool? primary,
    ScrollPhysics? physics,
  }) {
    return SingleChildScrollView(
      controller: controller,
      scrollDirection: scrollDirection,
      reverse: reverse,
      padding: padding,
      primary: primary,
      physics: physics ?? (kIsWeb ? const WebScrollPhysicsFixed() : null),
      child: child,
    );
  }

  /// إنشاء ListView محسن للويب
  static Widget createOptimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    bool? primary,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
    EdgeInsetsGeometry? padding,
  }) {
    return ListView.builder(
      controller: controller,
      scrollDirection: scrollDirection,
      reverse: reverse,
      primary: primary,
      physics: physics ?? (kIsWeb ? const WebScrollPhysicsFixed() : null),
      shrinkWrap: shrinkWrap,
      padding: padding,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      // تحسينات إضافية للويب
      cacheExtent: kIsWeb ? 1000.0 : 250.0,
      addAutomaticKeepAlives: !kIsWeb,
      addRepaintBoundaries: true,
    );
  }
}

/// ScrollBehavior محسن للويب
class WebScrollBehaviorFixed extends ScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.trackpad,
      };

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const WebScrollPhysicsFixed();
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (kIsWeb) {
      return RawScrollbar(
        controller: details.controller,
        thumbVisibility: true,
        trackVisibility: false,
        thickness: 8,
        radius: const Radius.circular(4),
        thumbColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.7),
        trackColor: Colors.transparent,
        crossAxisMargin: 2,
        mainAxisMargin: 2,
        child: child,
      );
    }
    return super.buildScrollbar(context, child, details);
  }

  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (kIsWeb) {
      // إزالة مؤشر الـ overscroll في الويب
      return child;
    }
    return super.buildOverscrollIndicator(context, child, details);
  }
}

/// ScrollPhysics محسن للويب لحل مشاكل Chrome
class WebScrollPhysicsFixed extends ScrollPhysics {
  const WebScrollPhysicsFixed({super.parent});

  @override
  WebScrollPhysicsFixed applyTo(ScrollPhysics? ancestor) {
    return WebScrollPhysicsFixed(parent: buildParent(ancestor));
  }

  @override
  double get minFlingVelocity => 100.0; // زيادة الحد الأدنى للسرعة

  @override
  double get maxFlingVelocity => 5000.0; // تقليل الحد الأقصى للسرعة

  @override
  double get dragStartDistanceMotionThreshold => 2.0; // تقليل المسافة المطلوبة للبدء

  @override
  SpringDescription get spring => const SpringDescription(
        mass: 0.3, // تقليل الكتلة لاستجابة أسرع
        stiffness: 200.0, // زيادة الصلابة
        damping: 1.0, // زيادة التخميد لتقليل الاهتزاز
      );

  @override
  Tolerance get tolerance => const Tolerance(
        velocity: 1.0,
        distance: 0.5,
      );

  @override
  Simulation? createBallisticSimulation(
    ScrollMetrics position,
    double velocity,
  ) {
    if (kIsWeb) {
      // تحسين محاكاة الحركة للويب
      final tolerance = this.tolerance;
      
      if (velocity.abs() < tolerance.velocity) {
        return null;
      }

      if (velocity > 0.0 && position.pixels >= position.maxScrollExtent) {
        return null;
      }

      if (velocity < 0.0 && position.pixels <= position.minScrollExtent) {
        return null;
      }

      // استخدام محاكاة مخصصة للويب
      return ClampingScrollSimulation(
        position: position.pixels,
        velocity: velocity,
        tolerance: tolerance,
      );
    }
    
    return super.createBallisticSimulation(position, velocity);
  }

  @override
  double applyPhysicsToUserOffset(ScrollMetrics position, double offset) {
    if (kIsWeb) {
      // تحسين استجابة التمرير للويب
      return offset * 1.2; // زيادة حساسية التمرير قليلاً
    }
    return super.applyPhysicsToUserOffset(position, offset);
  }
}

/// Widget مساعد لإصلاح مشاكل التمرير
class WebScrollWrapper extends StatelessWidget {
  final Widget child;
  final ScrollController? controller;
  final ScrollPhysics? physics;

  const WebScrollWrapper({
    super.key,
    required this.child,
    this.controller,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) return child;

    return ScrollConfiguration(
      behavior: WebScrollBehaviorFixed(),
      child: child,
    );
  }
}

/// مساعد لإنشاء ScrollController محسن للويب
class WebScrollController extends ScrollController {
  WebScrollController({
    super.initialScrollOffset = 0.0,
    super.keepScrollOffset = true,
    super.debugLabel,
  });

  @override
  Future<void> animateTo(
    double offset, {
    required Duration duration,
    required Curve curve,
  }) {
    if (kIsWeb) {
      // تحسين الرسوم المتحركة للويب
      return super.animateTo(
        offset,
        duration: Duration(milliseconds: (duration.inMilliseconds * 0.8).round()),
        curve: curve,
      );
    } else {
      return super.animateTo(offset, duration: duration, curve: curve);
    }
  }

  @override
  void jumpTo(double value) {
    if (kIsWeb) {
      // تحسين القفز للويب
      super.jumpTo(value);
      // إضافة تأخير صغير لضمان التحديث
      Future.delayed(const Duration(milliseconds: 1), () {
        if (hasClients) {
          notifyListeners();
        }
      });
    } else {
      super.jumpTo(value);
    }
  }
}
