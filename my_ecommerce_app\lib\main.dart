import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'routes/app_pages.dart';
import 'services/auth_service.dart';
import 'services/affiliate_service.dart';
import 'services/cart_service.dart';
import 'services/wishlist_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/admin_service.dart';
import 'controllers/auth_controller.dart';
import 'controllers/admin_controller.dart';
import 'controllers/affiliate_controller.dart';
import 'controllers/cart_controller.dart';
import 'utils/mouse_event_handler.dart';
import 'utils/layout_error_handler.dart';
import 'config/desktop_config.dart';
import 'config/app_config.dart';
import 'utils/app_tester.dart';
import 'utils/web_scroll_fix.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'utils/error_handler.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة معالج الأخطاء
  ErrorHandler.initialize();

  // تهيئة Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // تهيئة الخدمات
  await initServices();

  runApp(const MyApp());
}

// تهيئة الخدمات المطلوبة
Future<void> initServices() async {
  // تهيئة خدمة المصادقة
  Get.put(AuthService(), permanent: true);

  // تهيئة خدمة المسوقين
  Get.put(AffiliateService(), permanent: true);

  // تهيئة خدمة سلة التسوق
  Get.put(CartService(), permanent: true);

  // تهيئة خدمة المفضلة
  Get.put(WishlistService(), permanent: true);

  // تهيئة خدمة الإدارة
  Get.put(AdminService(), permanent: true);

  // تهيئة تحكم المصادقة
  Get.put(AuthController(), permanent: true);

  // تهيئة تحكم الإدارة (lazy loading)
  Get.lazyPut<AdminController>(() => AdminController(), fenix: true);

  // تهيئة تحكم المسوقين (lazy loading)
  Get.lazyPut<AffiliateController>(() => AffiliateController(), fenix: true);

  // تهيئة تحكم سلة التسوق
  Get.put(CartController(), permanent: true);
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    Widget app = GetMaterialApp(
      navigatorKey: navigatorKey,
      debugShowCheckedModeBanner: false,
      title: 'متجر النظارات العصري',
      theme: DesktopConfig.adjustThemeForDesktop(
        AppConfig.getThemeForPlatform(),
      ),
      scrollBehavior: DesktopConfig.scrollBehavior,
      initialRoute: '/', // البدء بالصفحة الرئيسية للضيوف
      getPages: AppPages.routes,
      locale: const Locale('ar', 'SA'), // اللغة العربية
      fallbackLocale: const Locale('en', 'US'),
    );

    // تطبيق إصلاحات التمرير للويب
    return WebScrollFix.applyScrollFix(app);
  }
}
