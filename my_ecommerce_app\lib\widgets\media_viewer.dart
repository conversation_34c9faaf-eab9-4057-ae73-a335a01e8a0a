import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';

// عارض الوسائط المتقدم
class MediaViewer extends StatefulWidget {
  final List<MediaItem> mediaItems;
  final int initialIndex;
  final String? heroTag;

  const MediaViewer({
    super.key,
    required this.mediaItems,
    this.initialIndex = 0,
    this.heroTag,
  });

  @override
  State<MediaViewer> createState() => _MediaViewerState();
}

class _MediaViewerState extends State<MediaViewer> {
  late PageController _pageController;
  late int _currentIndex;
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // تهيئة الفيديو إذا كان العنصر الأول فيديو
    if (widget.mediaItems[_currentIndex].type == MediaType.video) {
      _initializeVideo(widget.mediaItems[_currentIndex].url);
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _disposeVideo();
    super.dispose();
  }

  void _initializeVideo(String videoUrl) {
    _videoController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
    _chewieController = ChewieController(
      videoPlayerController: _videoController!,
      autoPlay: false,
      looping: false,
      showControls: true,
      materialProgressColors: ChewieProgressColors(
        playedColor: Colors.teal,
        handleColor: Colors.teal,
        backgroundColor: Colors.grey,
        bufferedColor: Colors.lightGreen,
      ),
      placeholder: Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.teal),
        ),
      ),
      autoInitialize: true,
    );
  }

  void _disposeVideo() {
    _chewieController?.dispose();
    _videoController?.dispose();
    _chewieController = null;
    _videoController = null;
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // إدارة الفيديو
    if (widget.mediaItems[index].type == MediaType.video) {
      _disposeVideo();
      _initializeVideo(widget.mediaItems[index].url);
    } else {
      _disposeVideo();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black.withValues(alpha: 0.5),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '${_currentIndex + 1} من ${widget.mediaItems.length}',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: _shareMedia,
          ),
          IconButton(
            icon: const Icon(Icons.download, color: Colors.white),
            onPressed: _downloadMedia,
          ),
        ],
      ),
      body: Stack(
        children: [
          // عارض الوسائط
          PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            builder: (BuildContext context, int index) {
              final mediaItem = widget.mediaItems[index];

              if (mediaItem.type == MediaType.video) {
                return PhotoViewGalleryPageOptions.customChild(
                  child: _buildVideoPlayer(),
                  heroAttributes: widget.heroTag != null
                      ? PhotoViewHeroAttributes(tag: '${widget.heroTag}_$index')
                      : null,
                );
              } else {
                return PhotoViewGalleryPageOptions(
                  imageProvider: CachedNetworkImageProvider(mediaItem.url),
                  initialScale: PhotoViewComputedScale.contained,
                  minScale: PhotoViewComputedScale.contained * 0.5,
                  maxScale: PhotoViewComputedScale.covered * 2.0,
                  heroAttributes: widget.heroTag != null
                      ? PhotoViewHeroAttributes(tag: '${widget.heroTag}_$index')
                      : null,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error, color: Colors.white, size: 50),
                          SizedBox(height: 16),
                          Text(
                            'خطأ في تحميل الصورة',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    );
                  },
                );
              }
            },
            itemCount: widget.mediaItems.length,
            loadingBuilder: (context, event) => const Center(
              child: CircularProgressIndicator(color: Colors.teal),
            ),
            backgroundDecoration: const BoxDecoration(
              color: Colors.black,
            ),
            pageController: _pageController,
            onPageChanged: _onPageChanged,
          ),

          // مؤشر الصفحات
          if (widget.mediaItems.length > 1)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: List.generate(
                      widget.mediaItems.length,
                      (index) => Container(
                        width: 8,
                        height: 8,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: index == _currentIndex
                              ? Colors.teal
                              : Colors.white.withValues(alpha: 0.5),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // معلومات الوسائط
          if (widget.mediaItems[_currentIndex].description != null)
            Positioned(
              bottom: 80,
              left: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.mediaItems[_currentIndex].description!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_chewieController == null) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.teal),
      );
    }

    return Center(
      child: AspectRatio(
        aspectRatio: _chewieController!.aspectRatio ?? 16 / 9,
        child: Chewie(controller: _chewieController!),
      ),
    );
  }

  void _shareMedia() {
    // تنفيذ مشاركة الوسائط
    Get.snackbar(
      'مشاركة',
      'تم نسخ الرابط',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.teal,
      colorText: Colors.white,
    );
  }

  void _downloadMedia() {
    // تنفيذ تحميل الوسائط
    Get.snackbar(
      'تحميل',
      'جاري تحميل الملف...',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.teal,
      colorText: Colors.white,
    );
  }
}

// عارض الصور المصغر للمنتج
class ProductMediaGallery extends StatefulWidget {
  final List<MediaItem> mediaItems;
  final double height;

  const ProductMediaGallery({
    super.key,
    required this.mediaItems,
    this.height = 300,
  });

  @override
  State<ProductMediaGallery> createState() => _ProductMediaGalleryState();
}

class _ProductMediaGalleryState extends State<ProductMediaGallery> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // العارض الرئيسي
        SizedBox(
          height: widget.height,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.mediaItems.length,
            itemBuilder: (context, index) {
              final mediaItem = widget.mediaItems[index];

              return GestureDetector(
                onTap: () {
                  // فتح العارض بملء الشاشة
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => MediaViewer(
                        mediaItems: widget.mediaItems,
                        initialIndex: index,
                        heroTag: 'product_media',
                      ),
                    ),
                  );
                },
                child: Hero(
                  tag: 'product_media_$index',
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: _buildMediaPreview(mediaItem),
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // المصغرات
        if (widget.mediaItems.length > 1)
          SizedBox(
            height: 60,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.mediaItems.length,
              itemBuilder: (context, index) {
                final mediaItem = widget.mediaItems[index];
                final isSelected = index == _currentIndex;

                return GestureDetector(
                  onTap: () {
                    _pageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    width: 60,
                    height: 60,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? Colors.teal : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: _buildMediaThumbnail(mediaItem),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildMediaPreview(MediaItem mediaItem) {
    if (mediaItem.type == MediaType.video) {
      return Stack(
        fit: StackFit.expand,
        children: [
          CachedNetworkImage(
            imageUrl: mediaItem.thumbnailUrl ?? mediaItem.url,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey[200],
              child: const Center(
                child: CircularProgressIndicator(color: Colors.teal),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey[200],
              child: const Icon(Icons.error, color: Colors.grey),
            ),
          ),
          const Center(
            child: Padding(
              padding: EdgeInsets.all(12),
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return CachedNetworkImage(
        imageUrl: mediaItem.url,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Colors.grey[200],
          child: const Center(
            child: CircularProgressIndicator(color: Colors.teal),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[200],
          child: const Icon(Icons.error, color: Colors.grey),
        ),
      );
    }
  }

  Widget _buildMediaThumbnail(MediaItem mediaItem) {
    return Stack(
      fit: StackFit.expand,
      children: [
        CachedNetworkImage(
          imageUrl: mediaItem.thumbnailUrl ?? mediaItem.url,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey[200],
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.teal,
                  strokeWidth: 2,
                ),
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey[200],
            child: const Icon(Icons.error, color: Colors.grey, size: 20),
          ),
        ),
        if (mediaItem.type == MediaType.video)
          const Center(
            child: Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 20,
            ),
          ),
      ],
    );
  }
}

// نموذج عنصر الوسائط
class MediaItem {
  final String url;
  final MediaType type;
  final String? thumbnailUrl;
  final String? description;
  final String? title;

  MediaItem({
    required this.url,
    required this.type,
    this.thumbnailUrl,
    this.description,
    this.title,
  });

  factory MediaItem.image(String url, {String? description, String? title}) {
    return MediaItem(
      url: url,
      type: MediaType.image,
      description: description,
      title: title,
    );
  }

  factory MediaItem.video(
    String url, {
    String? thumbnailUrl,
    String? description,
    String? title,
  }) {
    return MediaItem(
      url: url,
      type: MediaType.video,
      thumbnailUrl: thumbnailUrl,
      description: description,
      title: title,
    );
  }
}

enum MediaType { image, video }
