import 'package:get/get.dart';
import 'database_service.dart';
import '../models/wishlist.dart';
import '../models/product.dart';

class WishlistService extends GetxService {
  static WishlistService get instance => Get.find<WishlistService>();

  final Rx<Wishlist?> _wishlist = Rx<Wishlist?>(null);
  final RxInt _itemCount = 0.obs;

  Wishlist? get wishlist => _wishlist.value;
  RxInt get itemCount => _itemCount;


  @override
  Future<void> onInit() async {
    super.onInit();
    // لا تحميل تلقائي بدون userId
  }

  // تحديث عدد العناصر
  void _updateItemCount() {
    _itemCount.value = wishlist?.itemCount ?? 0;
  }


  // تحميل المفضلة من Firestore
  Future<void> loadWishlistFromFirestore(String userId) async {
    try {
      final db = DatabaseService();
      final doc = await db.getDocument('wishlists', userId);
      if (doc.exists && doc.data() != null) {
        _wishlist.value = Wishlist.fromJson(doc.data()!);
      } else {
        _wishlist.value = Wishlist.empty(userId);
        await db.setDocument('wishlists', userId, _wishlist.value!.toJson());
      }
      _updateItemCount();
    } catch (e) {
      print('Firestore wishlist load error: $e');
    }
  }


  // حفظ المفضلة في Firestore
  Future<void> saveWishlistToFirestore() async {
    try {
      if (_wishlist.value != null) {
        final db = DatabaseService();
        await db.setDocument('wishlists', _wishlist.value!.userId, _wishlist.value!.toJson());
      }
    } catch (e) {
      print('Firestore wishlist save error: $e');
    }
  }

  // إنشاء مفضلة جديدة للمستخدم
  Future<void> createWishlist(String userId) async {
    _wishlist.value = Wishlist.empty(userId);
    await saveWishlistToFirestore();
  }

  // إضافة منتج للمفضلة
  Future<bool> addToWishlist(Product product) async {
    try {
      if (_wishlist.value == null) return false;
      if (_wishlist.value!.hasProduct(product.id)) {
        Get.snackbar('موجود بالفعل', '${product.name} موجود في المفضلة بالفعل', snackPosition: SnackPosition.TOP);
        return false;
      }
      _wishlist.value = _wishlist.value!.addItem(product);
      _updateItemCount();
      await saveWishlistToFirestore();
      Get.snackbar('تمت الإضافة', 'تم إضافة ${product.name} إلى المفضلة', snackPosition: SnackPosition.TOP);
      return true;
    } catch (e) {
      print('Firestore wishlist add error: $e');
      Get.snackbar('خطأ', 'حدث خطأ أثناء إضافة المنتج للمفضلة', snackPosition: SnackPosition.TOP);
      return false;
    }
  }

  // حذف منتج من المفضلة
  Future<bool> removeFromWishlist(int productId) async {
    try {
      if (_wishlist.value == null) return false;
      _wishlist.value = _wishlist.value!.removeItem(productId);
      _updateItemCount();
      await saveWishlistToFirestore();
      Get.snackbar('تم الحذف', 'تم حذف المنتج من المفضلة', snackPosition: SnackPosition.TOP);
      return true;
    } catch (e) {
      print('Firestore wishlist remove error: $e');
      return false;
    }
  }

  // تبديل حالة المنتج في المفضلة
  Future<bool> toggleWishlist(Product product) async {
    if (_wishlist.value == null) return false;

    if (_wishlist.value!.hasProduct(product.id)) {
      return await removeFromWishlist(product.id);
    } else {
      return await addToWishlist(product);
    }
  }

  // تفريغ المفضلة
  Future<void> clearWishlist() async {
    try {
      if (_wishlist.value == null) return;
      _wishlist.value = _wishlist.value!.clear();
      _updateItemCount();
      await saveWishlistToFirestore();
      Get.snackbar('تم التفريغ', 'تم تفريغ المفضلة بنجاح', snackPosition: SnackPosition.TOP);
    } catch (e) {
      print('Firestore wishlist clear error: $e');
    }
  }

  // التحقق من وجود منتج في المفضلة
  bool hasProduct(int productId) {
    return _wishlist.value?.hasProduct(productId) ?? false;
  }

  // الحصول على المنتجات المتوفرة
  List<WishlistItem> getAvailableItems() {
    return _wishlist.value?.availableItems ?? [];
  }

  // الحصول على المنتجات غير المتوفرة
  List<WishlistItem> getUnavailableItems() {
    return _wishlist.value?.unavailableItems ?? [];
  }

  // نقل جميع المنتجات المتوفرة للسلة
  Future<void> moveAllToCart() async {
    final availableItems = getAvailableItems();
    if (availableItems.isEmpty) {
      Get.snackbar(
        'لا توجد منتجات',
        'لا توجد منتجات متوفرة في المفضلة',
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    // هنا يمكن إضافة منطق نقل المنتجات للسلة
    Get.snackbar(
      'تم النقل',
      'تم نقل ${availableItems.length} منتج إلى السلة',
      snackPosition: SnackPosition.TOP,
    );
  }

  // تسجيل خروج المستخدم - تفريغ المفضلة
  Future<void> onUserLogout(String userId) async {
    _wishlist.value = null;
    final db = DatabaseService();
    await db.deleteDocument('wishlists', userId);
  }

  // تسجيل دخول المستخدم - تحميل المفضلة
  Future<void> onUserLogin(String userId) async {
    await loadWishlistFromFirestore(userId);
    if (_wishlist.value == null || _wishlist.value!.userId != userId) {
      await createWishlist(userId);
    }
  }

  // إحصائيات المفضلة
  Map<String, dynamic> getWishlistStats() {
    if (_wishlist.value == null) {
      return {
        'totalItems': 0,
        'availableItems': 0,
        'unavailableItems': 0,
        'totalValue': 0.0,
      };
    }

    final available = _wishlist.value!.availableItems;
    final unavailable = _wishlist.value!.unavailableItems;
    final totalValue = available.fold(0.0, (sum, item) => sum + item.product.price);

    return {
      'totalItems': _wishlist.value!.itemCount,
      'availableItems': available.length,
      'unavailableItems': unavailable.length,
      'totalValue': totalValue,
    };
  }
}
