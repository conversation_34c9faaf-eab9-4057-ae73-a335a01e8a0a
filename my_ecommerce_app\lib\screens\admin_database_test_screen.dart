import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/admin_service.dart';
import '../services/admin_database_service.dart';

class AdminDatabaseTestScreen extends StatefulWidget {
  const AdminDatabaseTestScreen({super.key});

  @override
  State<AdminDatabaseTestScreen> createState() => _AdminDatabaseTestScreenState();
}

class _AdminDatabaseTestScreenState extends State<AdminDatabaseTestScreen> {
  final AdminService _adminService = Get.find<AdminService>();
  final AdminDatabaseService _dbService = AdminDatabaseService();
  Map<String, bool>? _testResults;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار قاعدة بيانات الإدارة'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبار خدمات الإدارة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('اختبار ربط خدمة الإدارة بـ Firestore'),
                    Text('- إدارة المستخدمين'),
                    Text('- إدارة المنتجات'),
                    Text('- إدارة الطلبات'),
                    Text('- إدارة طلبات المسوقين'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isRunning ? null : _runTests,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isRunning
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('جاري تشغيل الاختبارات...'),
                      ],
                    )
                  : const Text(
                      'تشغيل اختبارات خدمة الإدارة',
                      style: TextStyle(fontSize: 16),
                    ),
            ),
            const SizedBox(height: 16),
            if (_testResults != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نتائج الاختبارات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ..._testResults!.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Icon(
                                entry.value ? Icons.check_circle : Icons.error,
                                color: entry.value ? Colors.green : Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _getTestDisplayName(entry.key),
                                  style: TextStyle(
                                    color: entry.value ? Colors.green : Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Text(
                                entry.value ? 'نجح' : 'فشل',
                                style: TextStyle(
                                  color: entry.value ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _allTestsPassed() ? Colors.green.shade50 : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _allTestsPassed() ? Colors.green : Colors.red,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _allTestsPassed()
                              ? '🎉 جميع الاختبارات نجحت! خدمة الإدارة مربوطة بقاعدة البيانات بشكل صحيح.'
                              : '⚠️ بعض الاختبارات فشلت. يرجى التحقق من إعدادات قاعدة البيانات.',
                          style: TextStyle(
                            color: _allTestsPassed() ? Colors.green.shade800 : Colors.red.shade800,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _refreshAdminData,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('تحديث بيانات الإدارة'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _getStats,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('عرض الإحصائيات'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });

    try {
      final results = <String, bool>{};

      // اختبار جلب المستخدمين
      print('🔄 اختبار جلب المستخدمين...');
      try {
        final users = await _dbService.getAllUsers();
        results['get_users'] = true;
        print('✅ تم جلب ${users.length} مستخدم');
      } catch (e) {
        results['get_users'] = false;
        print('❌ فشل جلب المستخدمين: $e');
      }

      // اختبار جلب المنتجات
      print('🔄 اختبار جلب المنتجات...');
      try {
        final products = await _dbService.getAllProducts();
        results['get_products'] = true;
        print('✅ تم جلب ${products.length} منتج');
      } catch (e) {
        results['get_products'] = false;
        print('❌ فشل جلب المنتجات: $e');
      }

      // اختبار جلب الطلبات
      print('🔄 اختبار جلب الطلبات...');
      try {
        final orders = await _dbService.getAllOrders();
        results['get_orders'] = true;
        print('✅ تم جلب ${orders.length} طلب');
      } catch (e) {
        results['get_orders'] = false;
        print('❌ فشل جلب الطلبات: $e');
      }

      // اختبار جلب طلبات المسوقين
      print('🔄 اختبار جلب طلبات المسوقين...');
      try {
        final affiliateRequests = await _dbService.getAllAffiliateRequests();
        results['get_affiliate_requests'] = true;
        print('✅ تم جلب ${affiliateRequests.length} طلب مسوق');
      } catch (e) {
        results['get_affiliate_requests'] = false;
        print('❌ فشل جلب طلبات المسوقين: $e');
      }

      // اختبار الإحصائيات
      print('🔄 اختبار الإحصائيات...');
      try {
        final stats = await _dbService.getGeneralStats();
        results['get_stats'] = stats.isNotEmpty;
        print('✅ تم جلب الإحصائيات: ${stats.keys.join(', ')}');
      } catch (e) {
        results['get_stats'] = false;
        print('❌ فشل جلب الإحصائيات: $e');
      }

      setState(() {
        _testResults = results;
      });

      print('\n📊 نتائج اختبارات خدمة الإدارة:');
      results.forEach((test, result) {
        final icon = result ? '✅' : '❌';
        print('$icon $test: ${result ? 'نجح' : 'فشل'}');
      });

      final allPassed = results.values.every((result) => result);
      print('\n${allPassed ? '🎉' : '⚠️'} النتيجة النهائية: ${allPassed ? 'جميع الاختبارات نجحت' : 'بعض الاختبارات فشلت'}');

    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تشغيل الاختبارات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _refreshAdminData() async {
    try {
      await _adminService.refreshData();
      Get.snackbar(
        'تم',
        'تم تحديث بيانات الإدارة بنجاح',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث البيانات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _getStats() async {
    try {
      final stats = await _dbService.getGeneralStats();

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إحصائيات عامة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('إجمالي المستخدمين: ${stats['totalUsers'] ?? 0}'),
              Text('إجمالي المنتجات: ${stats['totalProducts'] ?? 0}'),
              Text('إجمالي الطلبات: ${stats['totalOrders'] ?? 0}'),
              Text('طلبات المسوقين: ${stats['totalAffiliateRequests'] ?? 0}'),
              Text('إجمالي الإيرادات: \$${stats['totalRevenue']?.toStringAsFixed(2) ?? '0.00'}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في جلب الإحصائيات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  String _getTestDisplayName(String testKey) {
    switch (testKey) {
      case 'get_users':
        return 'جلب المستخدمين';
      case 'get_products':
        return 'جلب المنتجات';
      case 'get_orders':
        return 'جلب الطلبات';
      case 'get_affiliate_requests':
        return 'جلب طلبات المسوقين';
      case 'get_stats':
        return 'جلب الإحصائيات';
      default:
        return testKey;
    }
  }

  bool _allTestsPassed() {
    return _testResults?.values.every((result) => result) ?? false;
  }
}
