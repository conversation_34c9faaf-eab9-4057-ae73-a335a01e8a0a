import 'package:cloud_firestore/cloud_firestore.dart' hide Order;
import '../models/order.dart';

/// خدمة قاعدة البيانات للطلبات
class OrderDatabaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== إدارة الطلبات ====================

  /// إنشاء طلب جديد
  Future<bool> createOrder(Order order) async {
    try {
      await _firestore.collection('orders').doc(order.id).set(order.toJson());
      return true;
    } catch (e) {
      print('خطأ في إنشاء الطلب: $e');
      return false;
    }
  }

  /// جلب جميع الطلبات
  Future<List<Order>> getAllOrders() async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Order.fromJson(doc.data())).toList();
    } catch (e) {
      print('خطأ في جلب الطلبات: $e');
      return [];
    }
  }

  /// جلب طلبات مستخدم معين
  Future<List<Order>> getUserOrders(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('customerId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Order.fromJson(doc.data())).toList();
    } catch (e) {
      print('خطأ في جلب طلبات المستخدم: $e');
      return [];
    }
  }

  /// جلب طلب بالمعرف
  Future<Order?> getOrderById(String orderId) async {
    try {
      final doc = await _firestore.collection('orders').doc(orderId).get();

      if (doc.exists) {
        return Order.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('خطأ في جلب الطلب: $e');
      return null;
    }
  }

  /// تحديث حالة الطلب
  Future<bool> updateOrderStatus(String orderId, OrderStatus status) async {
    try {
      await _firestore.collection('orders').doc(orderId).update({
        'status': status.toString(),
        'updatedAt': DateTime.now().toIso8601String(),
      });
      return true;
    } catch (e) {
      print('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// تحديث حالة الدفع
  Future<bool> updatePaymentStatus(String orderId, PaymentStatus status) async {
    try {
      await _firestore.collection('orders').doc(orderId).update({
        'paymentStatus': status.toString(),
        'updatedAt': DateTime.now().toIso8601String(),
      });
      return true;
    } catch (e) {
      print('خطأ في تحديث حالة الدفع: $e');
      return false;
    }
  }

  /// تحديث معلومات الشحن
  Future<bool> updateShippingInfo(
    String orderId, {
    String? trackingNumber,
    String? shippingCompany,
    DateTime? shippedAt,
    DateTime? deliveredAt,
  }) async {
    try {
      final updates = <String, dynamic>{
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (trackingNumber != null) {
        updates['trackingNumber'] = trackingNumber;
      }
      if (shippingCompany != null) {
        updates['shippingCompany'] = shippingCompany;
      }
      if (shippedAt != null) {
        updates['shippedAt'] = shippedAt.toIso8601String();
      }
      if (deliveredAt != null) {
        updates['deliveredAt'] = deliveredAt.toIso8601String();
      }

      await _firestore.collection('orders').doc(orderId).update(updates);
      return true;
    } catch (e) {
      print('خطأ في تحديث معلومات الشحن: $e');
      return false;
    }
  }

  /// إلغاء الطلب
  Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      await _firestore.collection('orders').doc(orderId).update({
        'status': OrderStatus.cancelled.toString(),
        'cancelReason': reason,
        'cancelledAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      });
      return true;
    } catch (e) {
      print('خطأ في إلغاء الطلب: $e');
      return false;
    }
  }

  /// جلب الطلبات بالحالة
  Future<List<Order>> getOrdersByStatus(OrderStatus status) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('status', isEqualTo: status.toString())
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Order.fromJson(doc.data())).toList();
    } catch (e) {
      print('خطأ في جلب الطلبات بالحالة: $e');
      return [];
    }
  }

  /// جلب الطلبات بحالة الدفع
  Future<List<Order>> getOrdersByPaymentStatus(PaymentStatus status) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('paymentStatus', isEqualTo: status.toString())
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Order.fromJson(doc.data())).toList();
    } catch (e) {
      print('خطأ في جلب الطلبات بحالة الدفع: $e');
      return [];
    }
  }

  /// جلب الطلبات في فترة زمنية
  Future<List<Order>> getOrdersByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('createdAt',
              isGreaterThanOrEqualTo: startDate.toIso8601String())
          .where('createdAt', isLessThanOrEqualTo: endDate.toIso8601String())
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Order.fromJson(doc.data())).toList();
    } catch (e) {
      print('خطأ في جلب الطلبات بالفترة الزمنية: $e');
      return [];
    }
  }

  /// البحث في الطلبات
  Future<List<Order>> searchOrders(String query) async {
    try {
      // البحث بمعرف الطلب
      final orderIdSnapshot = await _firestore
          .collection('orders')
          .where('id', isGreaterThanOrEqualTo: query)
          .where('id', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      final orders = <Order>[];
      final orderIds = <String>{};

      // إضافة نتائج البحث بمعرف الطلب
      for (final doc in orderIdSnapshot.docs) {
        if (!orderIds.contains(doc.id)) {
          orders.add(Order.fromJson(doc.data()));
          orderIds.add(doc.id);
        }
      }

      return orders;
    } catch (e) {
      print('خطأ في البحث في الطلبات: $e');
      return [];
    }
  }

  /// جلب إحصائيات الطلبات
  Future<Map<String, dynamic>> getOrderStats() async {
    try {
      final allOrdersSnapshot = await _firestore.collection('orders').get();

      int totalOrders = allOrdersSnapshot.docs.length;
      double totalRevenue = 0;
      int pendingOrders = 0;
      int processingOrders = 0;
      int shippedOrders = 0;
      int deliveredOrders = 0;
      int cancelledOrders = 0;

      for (final doc in allOrdersSnapshot.docs) {
        final data = doc.data();
        final totalAmount = (data['totalAmount'] as num?)?.toDouble() ?? 0;
        final status = data['status'] as String?;

        if (status != OrderStatus.cancelled.toString()) {
          totalRevenue += totalAmount;
        }

        switch (status) {
          case 'OrderStatus.pending':
            pendingOrders++;
            break;
          case 'OrderStatus.processing':
            processingOrders++;
            break;
          case 'OrderStatus.shipped':
            shippedOrders++;
            break;
          case 'OrderStatus.delivered':
            deliveredOrders++;
            break;
          case 'OrderStatus.cancelled':
            cancelledOrders++;
            break;
        }
      }

      // إحصائيات هذا الشهر
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      final monthlyOrdersSnapshot = await _firestore
          .collection('orders')
          .where('createdAt',
              isGreaterThanOrEqualTo: startOfMonth.toIso8601String())
          .where('createdAt', isLessThanOrEqualTo: endOfMonth.toIso8601String())
          .get();

      double monthlyRevenue = 0;
      for (final doc in monthlyOrdersSnapshot.docs) {
        final data = doc.data();
        final totalAmount = (data['totalAmount'] as num?)?.toDouble() ?? 0;
        monthlyRevenue += totalAmount;
      }

      return {
        'totalOrders': totalOrders,
        'totalRevenue': totalRevenue,
        'pendingOrders': pendingOrders,
        'processingOrders': processingOrders,
        'shippedOrders': shippedOrders,
        'deliveredOrders': deliveredOrders,
        'cancelledOrders': cancelledOrders,
        'monthlyOrders': monthlyOrdersSnapshot.docs.length,
        'monthlyRevenue': monthlyRevenue,
        'averageOrderValue': totalOrders > 0 ? totalRevenue / totalOrders : 0,
      };
    } catch (e) {
      print('خطأ في جلب إحصائيات الطلبات: $e');
      return {};
    }
  }

  /// جلب أفضل المنتجات مبيعاً
  Future<List<Map<String, dynamic>>> getBestSellingProducts(
      {int limit = 10}) async {
    try {
      final ordersSnapshot = await _firestore.collection('orders').get();
      final productSales = <String, Map<String, dynamic>>{};

      for (final doc in ordersSnapshot.docs) {
        final data = doc.data();
        final items = data['items'] as List<dynamic>? ?? [];

        for (final item in items) {
          final productId = item['productId'] as String?;
          final productName = item['productName'] as String?;
          final quantity = (item['quantity'] as num?)?.toInt() ?? 0;
          final price = (item['price'] as num?)?.toDouble() ?? 0;

          if (productId != null) {
            if (productSales.containsKey(productId)) {
              productSales[productId]!['totalQuantity'] += quantity;
              productSales[productId]!['totalRevenue'] += price * quantity;
            } else {
              productSales[productId] = {
                'productId': productId,
                'productName': productName ?? 'منتج غير معروف',
                'totalQuantity': quantity,
                'totalRevenue': price * quantity,
              };
            }
          }
        }
      }

      final sortedProducts = productSales.values.toList()
        ..sort((a, b) =>
            (b['totalQuantity'] as int).compareTo(a['totalQuantity'] as int));

      return sortedProducts.take(limit).toList();
    } catch (e) {
      print('خطأ في جلب أفضل المنتجات مبيعاً: $e');
      return [];
    }
  }

  /// جلب إحصائيات المبيعات الشهرية
  Future<List<Map<String, dynamic>>> getMonthlySalesStats(
      {int months = 12}) async {
    try {
      final now = DateTime.now();
      final monthlyStats = <Map<String, dynamic>>[];

      for (int i = 0; i < months; i++) {
        final monthDate = DateTime(now.year, now.month - i, 1);
        final startOfMonth = DateTime(monthDate.year, monthDate.month, 1);
        final endOfMonth = DateTime(monthDate.year, monthDate.month + 1, 0);

        final monthlyOrdersSnapshot = await _firestore
            .collection('orders')
            .where('createdAt',
                isGreaterThanOrEqualTo: startOfMonth.toIso8601String())
            .where('createdAt',
                isLessThanOrEqualTo: endOfMonth.toIso8601String())
            .get();

        double monthlyRevenue = 0;
        for (final doc in monthlyOrdersSnapshot.docs) {
          final data = doc.data();
          final totalAmount = (data['totalAmount'] as num?)?.toDouble() ?? 0;
          monthlyRevenue += totalAmount;
        }

        monthlyStats.add({
          'month': monthDate.month,
          'year': monthDate.year,
          'monthName': _getMonthName(monthDate.month),
          'totalOrders': monthlyOrdersSnapshot.docs.length,
          'totalRevenue': monthlyRevenue,
        });
      }

      return monthlyStats.reversed.toList();
    } catch (e) {
      print('خطأ في جلب إحصائيات المبيعات الشهرية: $e');
      return [];
    }
  }

  String _getMonthName(int month) {
    const monthNames = [
      '',
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return monthNames[month];
  }
}
