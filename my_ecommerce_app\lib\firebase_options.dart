// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDRYSPc4gG1KtMEChPaYxhhAlh3J9pNmD8',
    appId: '1:989826198249:web:d01c2f921c244b7bd0de78',
    messagingSenderId: '989826198249',
    projectId: 'my-ecommerce-app-990fb',
    authDomain: 'my-ecommerce-app-990fb.firebaseapp.com',
    storageBucket: 'my-ecommerce-app-990fb.firebasestorage.app',
    measurementId: 'G-QT4MYLGBDE',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDRYSPc4gG1KtMEChPaYxhhAlh3J9pNmD8',
    appId: '1:989826198249:android:a1b2c3d4e5f6g7h8i9j0k1l2',
    messagingSenderId: '989826198249',
    projectId: 'my-ecommerce-app-990fb',
    authDomain: 'my-ecommerce-app-990fb.firebaseapp.com',
    storageBucket: 'my-ecommerce-app-990fb.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDRYSPc4gG1KtMEChPaYxhhAlh3J9pNmD8',
    appId: '1:989826198249:ios:b2c3d4e5f6g7h8i9j0k1l2m3',
    messagingSenderId: '989826198249',
    projectId: 'my-ecommerce-app-990fb',
    authDomain: 'my-ecommerce-app-990fb.firebaseapp.com',
    storageBucket: 'my-ecommerce-app-990fb.firebasestorage.app',
    iosBundleId: 'com.example.myEcommerceApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDRYSPc4gG1KtMEChPaYxhhAlh3J9pNmD8',
    appId: '1:989826198249:ios:b2c3d4e5f6g7h8i9j0k1l2m3',
    messagingSenderId: '989826198249',
    projectId: 'my-ecommerce-app-990fb',
    authDomain: 'my-ecommerce-app-990fb.firebaseapp.com',
    storageBucket: 'my-ecommerce-app-990fb.firebasestorage.app',
    iosBundleId: 'com.example.myEcommerceApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDRYSPc4gG1KtMEChPaYxhhAlh3J9pNmD8',
    appId: '1:989826198249:web:d01c2f921c244b7bd0de78',
    messagingSenderId: '989826198249',
    projectId: 'my-ecommerce-app-990fb',
    authDomain: 'my-ecommerce-app-990fb.firebaseapp.com',
    storageBucket: 'my-ecommerce-app-990fb.firebasestorage.app',
    measurementId: 'G-QT4MYLGBDE',
  );
}
