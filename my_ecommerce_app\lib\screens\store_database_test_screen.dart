import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/product_database_service.dart';
import '../services/order_database_service.dart';
import '../models/product.dart';

class StoreDatabaseTestScreen extends StatefulWidget {
  const StoreDatabaseTestScreen({super.key});

  @override
  State<StoreDatabaseTestScreen> createState() =>
      _StoreDatabaseTestScreenState();
}

class _StoreDatabaseTestScreenState extends State<StoreDatabaseTestScreen> {
  final ProductDatabaseService _productDbService = ProductDatabaseService();
  final OrderDatabaseService _orderDbService = OrderDatabaseService();
  Map<String, bool>? _testResults;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار قاعدة بيانات المتجر'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبار خدمات المتجر',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('اختبار ربط خدمات المتجر بـ Firestore'),
                    Text('- إدارة المنتجات'),
                    Text('- إدارة الطلبات'),
                    Text('- البحث والفلترة'),
                    Text('- الإحصائيات'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isRunning ? null : _runTests,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isRunning
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('جاري تشغيل الاختبارات...'),
                      ],
                    )
                  : const Text(
                      'تشغيل اختبارات خدمات المتجر',
                      style: TextStyle(fontSize: 16),
                    ),
            ),
            const SizedBox(height: 16),
            if (_testResults != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نتائج الاختبارات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ..._testResults!.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Icon(
                                entry.value ? Icons.check_circle : Icons.error,
                                color: entry.value ? Colors.green : Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _getTestDisplayName(entry.key),
                                  style: TextStyle(
                                    color:
                                        entry.value ? Colors.green : Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Text(
                                entry.value ? 'نجح' : 'فشل',
                                style: TextStyle(
                                  color:
                                      entry.value ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _allTestsPassed()
                              ? Colors.green.shade50
                              : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                _allTestsPassed() ? Colors.green : Colors.red,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _allTestsPassed()
                              ? '🎉 جميع الاختبارات نجحت! خدمات المتجر مربوطة بقاعدة البيانات بشكل صحيح.'
                              : '⚠️ بعض الاختبارات فشلت. يرجى التحقق من إعدادات قاعدة البيانات.',
                          style: TextStyle(
                            color: _allTestsPassed()
                                ? Colors.green.shade800
                                : Colors.red.shade800,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _createTestProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('إنشاء منتج تجريبي'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _getStats,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('عرض الإحصائيات'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });

    try {
      final results = <String, bool>{};

      // اختبار جلب المنتجات
      debugPrint('🔄 اختبار جلب المنتجات...');
      try {
        final products = await _productDbService.getAllProducts();
        results['get_products'] = true;
        debugPrint('✅ تم جلب ${products.length} منتج');
      } catch (e) {
        results['get_products'] = false;
        debugPrint('❌ فشل جلب المنتجات: $e');
      }

      // اختبار البحث في المنتجات
      debugPrint('🔄 اختبار البحث في المنتجات...');
      try {
        final searchResults = await _productDbService.searchProducts('نظارة');
        results['search_products'] = true;
        debugPrint('✅ البحث يعمل، تم العثور على ${searchResults.length} منتج');
      } catch (e) {
        results['search_products'] = false;
        debugPrint('❌ فشل البحث في المنتجات: $e');
      }

      // اختبار جلب المنتجات المميزة
      debugPrint('🔄 اختبار جلب المنتجات المميزة...');
      try {
        final featuredProducts = await _productDbService.getFeaturedProducts();
        results['get_featured_products'] = true;
        debugPrint('✅ تم جلب ${featuredProducts.length} منتج مميز');
      } catch (e) {
        results['get_featured_products'] = false;
        debugPrint('❌ فشل جلب المنتجات المميزة: $e');
      }

      // اختبار جلب الطلبات
      debugPrint('🔄 اختبار جلب الطلبات...');
      try {
        final orders = await _orderDbService.getAllOrders();
        results['get_orders'] = true;
        debugPrint('✅ تم جلب ${orders.length} طلب');
      } catch (e) {
        results['get_orders'] = false;
        debugPrint('❌ فشل جلب الطلبات: $e');
      }

      // اختبار إحصائيات المنتجات
      debugPrint('🔄 اختبار إحصائيات المنتجات...');
      try {
        final productStats = await _productDbService.getProductStats();
        results['get_product_stats'] = productStats.isNotEmpty;
        debugPrint('✅ تم جلب إحصائيات المنتجات: ${productStats.keys.join(', ')}');
      } catch (e) {
        results['get_product_stats'] = false;
        debugPrint('❌ فشل جلب إحصائيات المنتجات: $e');
      }

      // اختبار إحصائيات الطلبات
      debugPrint('🔄 اختبار إحصائيات الطلبات...');
      try {
        final orderStats = await _orderDbService.getOrderStats();
        results['get_order_stats'] = orderStats.isNotEmpty;
        debugPrint('✅ تم جلب إحصائيات الطلبات: ${orderStats.keys.join(', ')}');
      } catch (e) {
        results['get_order_stats'] = false;
        debugPrint('❌ فشل جلب إحصائيات الطلبات: $e');
      }

      setState(() {
        _testResults = results;
      });

      debugPrint('\n📊 نتائج اختبارات خدمات المتجر:');
      results.forEach((test, result) {
        final icon = result ? '✅' : '❌';
        debugPrint('$icon $test: ${result ? 'نجح' : 'فشل'}');
      });

      final allPassed = results.values.every((result) => result);
      debugPrint(
          '\n${allPassed ? '🎉' : '⚠️'} النتيجة النهائية: ${allPassed ? 'جميع الاختبارات نجحت' : 'بعض الاختبارات فشلت'}');
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تشغيل الاختبارات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _createTestProduct() async {
    try {
      final testProduct = Product(
        id: DateTime.now().millisecondsSinceEpoch,
        name: 'نظارة تجريبية ${DateTime.now().millisecondsSinceEpoch}',
        description: 'منتج تجريبي لاختبار النظام',
        price: 299.99,
        originalPrice: 399.99,
        category: 'نظارات شمسية',
        brand: 'علامة تجريبية',
        stockQuantity: 10,
        imageUrls: [
          'https://via.placeholder.com/300x300.png?text=Test+Product'
        ],
        type: EyewearType.sunglasses,
        targetGender: Gender.unisex,
        specs: EyewearSpecs(
          frameMaterial: FrameMaterial.plastic,
          lensType: LensType.polarized,
          frameColor: 'أسود',
          lensColor: 'رمادي',
          frameSize: 'متوسط',
          lensWidth: 50,
          bridgeWidth: 18,
          templeLength: 140,
        ),
        createdAt: DateTime.now(),
      );

      final success = await _productDbService.addProduct(testProduct);

      if (success) {
        Get.snackbar(
          'تم',
          'تم إنشاء منتج تجريبي بنجاح',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في إنشاء المنتج التجريبي',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _getStats() async {
    try {
      final productStats = await _productDbService.getProductStats();
      final orderStats = await _orderDbService.getOrderStats();

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إحصائيات المتجر'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('إحصائيات المنتجات:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Text('إجمالي المنتجات: ${productStats['totalProducts'] ?? 0}'),
                Text(
                    'المنتجات المتاحة: ${productStats['availableProducts'] ?? 0}'),
                Text(
                    'المنتجات غير المتاحة: ${productStats['outOfStockProducts'] ?? 0}'),
                Text(
                    'قيمة المخزون: \$${(productStats['totalInventoryValue'] ?? 0).toStringAsFixed(2)}'),
                const SizedBox(height: 16),
                const Text('إحصائيات الطلبات:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Text('إجمالي الطلبات: ${orderStats['totalOrders'] ?? 0}'),
                Text(
                    'إجمالي الإيرادات: \$${(orderStats['totalRevenue'] ?? 0).toStringAsFixed(2)}'),
                Text('الطلبات المعلقة: ${orderStats['pendingOrders'] ?? 0}'),
                Text('الطلبات المكتملة: ${orderStats['deliveredOrders'] ?? 0}'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في جلب الإحصائيات: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  String _getTestDisplayName(String testKey) {
    switch (testKey) {
      case 'get_products':
        return 'جلب المنتجات';
      case 'search_products':
        return 'البحث في المنتجات';
      case 'get_featured_products':
        return 'جلب المنتجات المميزة';
      case 'get_orders':
        return 'جلب الطلبات';
      case 'get_product_stats':
        return 'إحصائيات المنتجات';
      case 'get_order_stats':
        return 'إحصائيات الطلبات';
      default:
        return testKey;
    }
  }

  bool _allTestsPassed() {
    return _testResults?.values.every((result) => result) ?? false;
  }
}
