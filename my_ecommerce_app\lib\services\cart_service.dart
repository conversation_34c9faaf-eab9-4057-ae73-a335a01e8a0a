import 'package:get/get.dart';
import 'database_service.dart';
import '../models/cart.dart';
import '../models/product.dart';

class CartService extends GetxService {
  static CartService get instance => Get.find<CartService>();

  final Rx<Cart?> _cart = Rx<Cart?>(null);
  final RxInt _itemCount = 0.obs;
  final RxDouble _totalPrice = 0.0.obs;

  Cart? get cart => _cart.value;
  RxInt get itemCount => _itemCount;
  RxDouble get totalPrice => _totalPrice;


  @override
  Future<void> onInit() async {
    super.onInit();
    // لا تحميل تلقائي بدون userId
  }

  // تحديث القيم التفاعلية
  void _updateReactiveValues() {
    _itemCount.value = cart?.totalItems ?? 0;
    _totalPrice.value = cart?.totalPrice ?? 0.0;
  }


  // تحميل السلة من Firestore
  Future<void> loadCartFromFirestore(String userId) async {
    try {
      final db = DatabaseService();
      final doc = await db.getDocument('carts', userId);
      if (doc.exists && doc.data() != null) {
        _cart.value = Cart.fromJson(doc.data()!);
      } else {
        _cart.value = Cart.empty(userId);
        await db.setDocument('carts', userId, _cart.value!.toJson());
      }
      _updateReactiveValues();
    } catch (e) {
      print('Firestore load error: $e');
    }
  }


  // حفظ السلة في Firestore
  Future<void> saveCartToFirestore() async {
    try {
      if (_cart.value != null) {
        final db = DatabaseService();
        await db.setDocument('carts', _cart.value!.userId, _cart.value!.toJson());
      }
    } catch (e) {
      print('Firestore save error: $e');
    }
  }

  // إنشاء سلة جديدة للمستخدم
  Future<void> createCart(String userId) async {
    _cart.value = Cart.empty(userId);
    _updateReactiveValues();
    await saveCartToFirestore();
  }

  // إضافة منتج للسلة
  Future<bool> addToCart(Product product, {int quantity = 1}) async {
    try {
      if (_cart.value == null) return false;
      if (product.stockQuantity < quantity) {
        Get.snackbar('غير متوفر', 'الكمية المطلوبة غير متوفرة في المخزون', snackPosition: SnackPosition.TOP);
        return false;
      }
      _cart.value = _cart.value!.addItem(product, quantity: quantity);
      _updateReactiveValues();
      await saveCartToFirestore();
      Get.snackbar('تمت الإضافة', 'تم إضافة ${product.name} إلى السلة', snackPosition: SnackPosition.TOP);
      return true;
    } catch (e) {
      print('Firestore add error: $e');
      Get.snackbar('خطأ', 'حدث خطأ أثناء إضافة المنتج للسلة', snackPosition: SnackPosition.TOP);
      return false;
    }
  }

  // تحديث كمية منتج في السلة
  Future<bool> updateQuantity(int productId, int quantity) async {
    try {
      if (_cart.value == null) return false;
      _cart.value = _cart.value!.updateItemQuantity(productId, quantity);
      _updateReactiveValues();
      await saveCartToFirestore();
      return true;
    } catch (e) {
      print('Firestore update error: $e');
      return false;
    }
  }

  // حذف منتج من السلة
  Future<bool> removeFromCart(int productId) async {
    try {
      if (_cart.value == null) return false;
      _cart.value = _cart.value!.removeItem(productId);
      _updateReactiveValues();
      await saveCartToFirestore();
      Get.snackbar('تم الحذف', 'تم حذف المنتج من السلة', snackPosition: SnackPosition.TOP);
      return true;
    } catch (e) {
      print('Firestore remove error: $e');
      return false;
    }
  }

  // تفريغ السلة
  Future<void> clearCart() async {
    try {
      if (_cart.value == null) return;
      _cart.value = _cart.value!.clear();
      _updateReactiveValues();
      await saveCartToFirestore();
      Get.snackbar('تم التفريغ', 'تم تفريغ السلة بنجاح', snackPosition: SnackPosition.TOP);
    } catch (e) {
      print('Firestore clear error: $e');
    }
  }

  // التحقق من وجود منتج في السلة
  bool hasProduct(int productId) {
    return _cart.value?.hasProduct(productId) ?? false;
  }

  // الحصول على كمية منتج في السلة
  int getProductQuantity(int productId) {
    final item = _cart.value?.getItem(productId);
    return item?.quantity ?? 0;
  }

  // حساب إجمالي السعر مع الضرائب والشحن
  double getTotalWithTaxAndShipping({
    double taxRate = 0.15,
    double shippingCost = 10.0,
    double freeShippingThreshold = 100.0,
  }) {
    final subtotal = _cart.value?.totalPrice ?? 0.0;
    final tax = subtotal * taxRate;
    final shipping = subtotal >= freeShippingThreshold ? 0.0 : shippingCost;

    return subtotal + tax + shipping;
  }

  // تسجيل خروج المستخدم - تفريغ السلة
  Future<void> onUserLogout(String userId) async {
    _cart.value = null;
    _updateReactiveValues();
    final db = DatabaseService();
    await db.deleteDocument('carts', userId);
  }

  // تسجيل دخول المستخدم - تحميل السلة
  Future<void> onUserLogin(String userId) async {
    await loadCartFromFirestore(userId);
    if (_cart.value == null || _cart.value!.userId != userId) {
      await createCart(userId);
    }
    _updateReactiveValues();
  }
}
