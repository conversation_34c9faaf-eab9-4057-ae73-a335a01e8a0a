import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class ErrorHandler {
  // معالج الأخطاء الرئيسي
  static void initialize() {
    if (kDebugMode) {
      FlutterError.onError = (FlutterErrorDetails details) {
        _handleFlutterError(details);
      };
    }
  }

  // معالجة أخطاء Flutter
  static void _handleFlutterError(FlutterErrorDetails details) {
    final String errorMessage = details.exception.toString();
    
    // قائمة الأخطاء المعروفة التي يجب تجاهلها
    final List<String> ignoredErrors = [
      'fontSize.isFinite',
      'text_scaler.dart',
      'shifted_box.dart',
      'Render box sizing error',
      'MouseTracker',
      'Could not find a set of Noto fonts',
    ];

    // تحقق من الأخطاء المتجاهلة
    for (String ignoredError in ignoredErrors) {
      if (errorMessage.contains(ignoredError)) {
        // لا تطبع هذه الأخطاء
        return;
      }
    }

    // طباعة الأخطاء المهمة فقط
    if (kDebugMode) {
      print('🚨 خطأ مهم: ${details.exception}');
      print('📍 المكان: ${details.library}');
      if (details.stack != null) {
        print('📋 التفاصيل: ${details.stack}');
      }
    }
  }

  // معالجة الأخطاء العامة
  static void handleError(dynamic error, StackTrace? stackTrace) {
    if (kDebugMode) {
      print('🚨 خطأ عام: $error');
      if (stackTrace != null) {
        print('📋 Stack Trace: $stackTrace');
      }
    }
  }

  // عرض رسالة خطأ للمستخدم
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // معالج آمن للعمليات غير المتزامنة
  static Future<T?> safeAsyncOperation<T>(
    Future<T> Function() operation, {
    T? fallbackValue,
    String? errorMessage,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      handleError(error, stackTrace);
      return fallbackValue;
    }
  }

  // معالج آمن للعمليات المتزامنة
  static T? safeSyncOperation<T>(
    T Function() operation, {
    T? fallbackValue,
    String? errorMessage,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      handleError(error, stackTrace);
      return fallbackValue;
    }
  }
}