import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

class AuthController extends GetxController {
  final AuthService _authService = AuthService.instance;

  // Form controllers
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final phoneController = TextEditingController();
  final referralCodeController = TextEditingController();

  // Form keys
  final loginFormKey = GlobalKey<FormState>();
  final registerFormKey = GlobalKey<FormState>();
  final resetPasswordFormKey = GlobalKey<FormState>();

  // Observable variables
  final RxBool isPasswordVisible = false.obs;
  final RxBool isConfirmPasswordVisible = false.obs;
  final RxBool rememberMe = false.obs;
  final Rx<UserRole> selectedRole = UserRole.customer.obs;

  // Getters
  bool get isLoading => _authService.isLoading;
  bool get isLoggedIn => _authService.isLoggedIn;
  User? get currentUser => _authService.currentUser;

  @override
  void onClose() {
    // تنظيف الـ controllers
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    phoneController.dispose();
    referralCodeController.dispose();
    super.onClose();
  }

  // تسجيل الدخول
  Future<void> login() async {
    if (!loginFormKey.currentState!.validate()) return;

    final success = await _authService.login(
      emailController.text.trim(),
      passwordController.text,
    );

    if (success) {
      _clearLoginForm();
      _navigateBasedOnRole();
    }
  }

  // إنشاء حساب جديد
  Future<void> register() async {
    if (!registerFormKey.currentState!.validate()) return;

    final success = await _authService.register(
      email: emailController.text.trim(),
      password: passwordController.text,
      firstName: firstNameController.text.trim(),
      lastName: lastNameController.text.trim(),
      phoneNumber: phoneController.text.trim(),
      role: selectedRole.value,
      referralCode: referralCodeController.text.trim().isEmpty
          ? null
          : referralCodeController.text.trim(),
    );

    if (success) {
      _clearRegisterForm();
      _navigateBasedOnRole();
    }
  }

  // إعادة تعيين كلمة المرور
  Future<void> resetPassword() async {
    if (!resetPasswordFormKey.currentState!.validate()) return;

    final success = await _authService.resetPassword(emailController.text.trim());

    if (success) {
      emailController.clear();
      Get.back(); // العودة لشاشة تسجيل الدخول
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    await _authService.logout();
    Get.offAllNamed('/login');
  }

  // التنقل حسب نوع المستخدم
  void _navigateBasedOnRole() {
    if (currentUser == null) return;

    switch (currentUser!.role) {
      case UserRole.admin:
        Get.offAllNamed('/admin-dashboard');
        break;
      case UserRole.affiliate:
        // المسوقون يذهبون للصفحة الرئيسية المخصصة لهم
        Get.offAllNamed('/affiliate-home');
        break;
      case UserRole.customer:
        Get.offAllNamed('/');
        break;
    }
  }

  // تنظيف نموذج تسجيل الدخول
  void _clearLoginForm() {
    emailController.clear();
    passwordController.clear();
    isPasswordVisible.value = false;
  }

  // تنظيف نموذج التسجيل
  void _clearRegisterForm() {
    emailController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    referralCodeController.clear();
    isPasswordVisible.value = false;
    isConfirmPasswordVisible.value = false;
    selectedRole.value = UserRole.customer;
  }

  // تبديل رؤية كلمة المرور
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  // تبديل رؤية تأكيد كلمة المرور
  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  // تبديل تذكرني
  void toggleRememberMe() {
    rememberMe.value = !rememberMe.value;
  }

  // تغيير نوع المستخدم
  void changeUserRole(UserRole role) {
    selectedRole.value = role;
  }

  // التحقق من صحة البريد الإلكتروني
  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    if (!_authService.isValidEmail(value)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }
    return null;
  }

  // التحقق من صحة كلمة المرور
  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    if (!_authService.isValidPassword(value)) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  // التحقق من تطابق كلمة المرور
  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى تأكيد كلمة المرور';
    }
    if (value != passwordController.text) {
      return 'كلمة المرور غير متطابقة';
    }
    return null;
  }

  // التحقق من صحة الاسم
  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال الاسم';
    }
    if (value.length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    return null;
  }

  // التحقق من صحة رقم الهاتف
  String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }
    if (value.length < 10) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  // التحقق من كود الإحالة (اختياري)
  String? validateReferralCode(String? value) {
    // كود الإحالة اختياري، لذا لا نحتاج للتحقق إذا كان فارغاً
    if (value != null && value.isNotEmpty && value.length < 3) {
      return 'كود الإحالة غير صحيح';
    }
    return null;
  }

  // الحصول على نص نوع المستخدم
  String getUserRoleText(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'عميل';
      case UserRole.affiliate:
        return 'مسوق';
      case UserRole.admin:
        return 'مدير';
    }
  }

  // الحصول على وصف نوع المستخدم
  String getUserRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'للتسوق وشراء المنتجات';
      case UserRole.affiliate:
        return 'للتسويق وكسب العمولات';
      case UserRole.admin:
        return 'لإدارة النظام';
    }
  }
}
