import 'package:cloud_firestore/cloud_firestore.dart'
    hide Order; // Hide Firestore's Order to prevent conflict
import 'package:get/get.dart';
import 'package:my_ecommerce_app/models/cart.dart';
// إذا ظهر خط أحمر تحت هذا الاستيراد، تأكد من إضافة uuid في pubspec.yaml:
// uuid: ^3.0.7
// ثم شغل flutter pub get
import 'package:uuid/uuid.dart';

import '../models/order.dart'; // This single import brings in your Order, OrderItem, and ShippingAddress models
import 'auth_service.dart';
import 'database_service.dart';

class OrderService extends GetxService {
  static OrderService get instance => Get.find();

  final DatabaseService _db = DatabaseService();
  final AuthService _auth = AuthService.instance;

  /// Creates a new order in Firestore from the user's cart.
  Future<Order?> createOrderFromCart(Cart cart, ShippingAddress address,
      {double shippingCost = 25.0, double taxRate = 0.15}) async {
    final user = _auth.currentUser;
    if (user == null) {
      Get.snackbar('خطأ', 'يجب تسجيل الدخول لإنشاء طلب.');
      return null;
    }

    if (cart.items.isEmpty) {
      Get.snackbar('خطأ', 'سلة التسوق فارغة.');
      return null;
    }

    try {
      final orderId = const Uuid().v4();

      final List<OrderItem> orderItems = cart.items.map((cartItem) {
        return OrderItem(
          productId: cartItem.product.id.toString(),
          productName: cartItem.product.name,
          productImage: cartItem.product.imageUrls.isNotEmpty
              ? cartItem.product.imageUrls.first
              : '',
          quantity: cartItem.quantity,
          unitPrice: cartItem.product.price,
          totalPrice: cartItem.totalPrice,
        );
      }).toList();

      final subtotal = cart.totalPrice;
      final tax = subtotal * taxRate;
      final total = subtotal + tax + shippingCost;

      final newOrder = Order(
        id: orderId,
        customerId: user.id,
        items: orderItems,
        subtotal: subtotal,
        shippingCost: shippingCost,
        tax: tax,
        totalAmount: total,
        shippingAddress: address,
        createdAt: DateTime.now(),
        status: OrderStatus.pending,
        paymentStatus: PaymentStatus.pending,
      );

      await _db.setDocument('orders', orderId, newOrder.toJson());
      Get.snackbar('نجاح', 'تم إرسال طلبك بنجاح!');
      return newOrder;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إتمام الطلب: $e');
      return null;
    }
  }

  /// Fetches all orders for the currently logged-in user.
  Future<List<Order>> getUserOrders() async {
    final user = _auth.currentUser;
    if (user == null) return [];

    try {
      // We use the FirebaseFirestore instance directly for querying
      final querySnapshot = await FirebaseFirestore.instance
          .collection('orders')
          .where('customerId', isEqualTo: user.id)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Order.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print("خطأ في جلب طلبات المستخدم: $e");
      Get.snackbar('خطأ', 'فشل في جلب الطلبات.');
      return [];
    }
  }
}
