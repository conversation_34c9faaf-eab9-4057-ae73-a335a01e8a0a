import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/user.dart';
import '../models/commission.dart';
import '../models/affiliate_link.dart';
import '../models/product.dart';
import '../models/app_settings.dart';
import '../models/affiliate_request.dart';
import 'affiliate_database_service.dart';

class AffiliateService extends GetxService {
  static AffiliateService get instance => Get.find();

  // خدمة قاعدة البيانات
  final AffiliateDatabaseService _dbService = AffiliateDatabaseService();

  // إعدادات العمولة
  final AppSettings _settings = demoAppSettings;

  // قوائم البيانات
  final RxList<AffiliateLink> _affiliateLinks = <AffiliateLink>[].obs;
  final RxList<Commission> _commissions = <Commission>[].obs;
  final RxList<ClickTracking> _clickTrackings = <ClickTracking>[].obs;
  final RxList<User> _affiliates = <User>[].obs;
  final RxList<AffiliateRequest> _affiliateRequests = <AffiliateRequest>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  // تهيئة البيانات من قاعدة البيانات
  Future<void> _initializeData() async {
    await loadAffiliatesFromDatabase();
    await loadAffiliateRequestsFromDatabase();
    _loadDemoData(); // للبيانات التجريبية المؤقتة
  }

  // تحميل البيانات التجريبية
  void _loadDemoData() {
    _commissions.addAll(demoCommissions);
    _affiliateLinks.addAll(_generateDemoLinks());
    _clickTrackings.addAll(_generateDemoClicks());
  }

  // ==================== دوال قاعدة البيانات ====================

  /// تحميل المسوقين من قاعدة البيانات
  Future<void> loadAffiliatesFromDatabase() async {
    try {
      final affiliates = await _dbService.getAllAffiliates();
      _affiliates.assignAll(affiliates);
    } catch (e) {
      debugPrint('خطأ في تحميل المسوقين: $e');
    }
  }

  /// تحميل طلبات المسوقين من قاعدة البيانات
  Future<void> loadAffiliateRequestsFromDatabase() async {
    try {
      // هذه الدالة ستحتاج للمستخدم الحالي
      // سنتركها فارغة الآن وننفذها عند الحاجة
    } catch (e) {
      debugPrint('خطأ في تحميل طلبات المسوقين: $e');
    }
  }

  /// إرسال طلب انضمام للتسويق
  Future<bool> submitAffiliateRequest(AffiliateRequest request) async {
    try {
      final success = await _dbService.submitAffiliateRequest(request);
      if (success) {
        _affiliateRequests.add(request);
        Get.snackbar('تم الإرسال', 'تم إرسال طلب الانضمام للتسويق بنجاح');
      }
      return success;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إرسال الطلب: $e');
      return false;
    }
  }

  /// جلب طلبات المسوق للمستخدم الحالي
  Future<List<AffiliateRequest>> getUserAffiliateRequests(String userId) async {
    try {
      return await _dbService.getUserAffiliateRequests(userId);
    } catch (e) {
      debugPrint('خطأ في جلب طلبات المسوق: $e');
      return [];
    }
  }

  /// البحث عن مسوق بالكود
  Future<User?> getAffiliateByCode(String affiliateCode) async {
    try {
      return await _dbService.getAffiliateByCode(affiliateCode);
    } catch (e) {
      debugPrint('خطأ في البحث عن المسوق: $e');
      return null;
    }
  }

  // إنشاء رابط تسويقي جديد
  Future<AffiliateLink> createAffiliateLink({
    required String affiliateId,
    required String affiliateCode,
    required LinkType type,
    String? productId,
    String? categoryId,
    String? customUrl,
  }) async {
    // محاكاة تأخير الشبكة
    await Future.delayed(const Duration(milliseconds: 500));

    final trackingCode = _generateTrackingCode();
    final originalUrl =
        _buildOriginalUrl(type, productId, categoryId, customUrl);
    final shortUrl = _generateShortUrl(trackingCode);

    final link = AffiliateLink(
      id: 'link_${DateTime.now().millisecondsSinceEpoch}',
      affiliateId: affiliateId,
      affiliateCode: affiliateCode,
      type: type,
      productId: productId,
      categoryId: categoryId,
      originalUrl: originalUrl,
      shortUrl: shortUrl,
      trackingCode: trackingCode,
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(const Duration(days: 365)), // صالح لسنة
    );

    _affiliateLinks.add(link);

    Get.snackbar(
      'تم إنشاء الرابط',
      'تم إنشاء رابط تسويقي جديد بنجاح',
      snackPosition: SnackPosition.TOP,
    );

    return link;
  }

  // الحصول على روابط المسوق
  List<AffiliateLink> getAffiliateLinks(String affiliateId) {
    return _affiliateLinks
        .where((link) => link.affiliateId == affiliateId)
        .toList();
  }

  // الحصول على عمولات المسوق
  List<Commission> getAffiliateCommissions(String affiliateId) {
    return _commissions
        .where((commission) => commission.affiliateId == affiliateId)
        .toList();
  }

  // الحصول على إحصائيات المسوق
  Map<String, dynamic> getAffiliateStats(String affiliateId) {
    final commissions = getAffiliateCommissions(affiliateId);
    final links = getAffiliateLinks(affiliateId);

    final totalEarnings = commissions
        .where((c) => c.isPaid)
        .fold(0.0, (sum, c) => sum + c.commissionAmount);

    final pendingEarnings = commissions
        .where((c) => c.isEarned && !c.isPaid)
        .fold(0.0, (sum, c) => sum + c.commissionAmount);

    final totalClicks = links.fold(0, (sum, link) => sum + link.clickCount);
    final totalConversions =
        links.fold(0, (sum, link) => sum + link.conversionCount);

    return {
      'totalEarnings': totalEarnings,
      'pendingEarnings': pendingEarnings,
      'totalCommissions': commissions.length,
      'paidCommissions': commissions.where((c) => c.isPaid).length,
      'totalClicks': totalClicks,
      'totalConversions': totalConversions,
      'conversionRate':
          totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0.0,
      'averageOrderValue': commissions.isNotEmpty
          ? commissions.fold(0.0, (sum, c) => sum + c.orderAmount) /
              commissions.length
          : 0.0,
    };
  }

  // تسجيل نقرة على رابط تسويقي
  Future<void> trackClick({
    required String trackingCode,
    required String ipAddress,
    required String userAgent,
    String? referrer,
    String? customerId,
  }) async {
    final click = ClickTracking(
      id: 'click_${DateTime.now().millisecondsSinceEpoch}',
      linkId: '', // سيتم تحديثه
      trackingCode: trackingCode,
      customerId: customerId,
      ipAddress: ipAddress,
      userAgent: userAgent,
      referrer: referrer,
      clickedAt: DateTime.now(),
    );

    _clickTrackings.add(click);

    // تحديث عداد النقرات للرابط
    final linkIndex =
        _affiliateLinks.indexWhere((link) => link.trackingCode == trackingCode);
    if (linkIndex != -1) {
      final link = _affiliateLinks[linkIndex];
      _affiliateLinks[linkIndex] =
          link.copyWith(clickCount: link.clickCount + 1);
    }
  }

  // إنشاء عمولة عند تأكيد الطلب
  Future<Commission> createCommission({
    required String affiliateId,
    required String customerId,
    required String orderId,
    required String productId,
    required String trackingCode,
    required double orderAmount,
  }) async {
    final commissionRate = _settings.commissionSettings.defaultCommissionRate;
    final commissionAmount =
        Commission.calculateCommission(orderAmount, commissionRate);

    final commission = Commission(
      id: 'comm_${DateTime.now().millisecondsSinceEpoch}',
      affiliateId: affiliateId,
      customerId: customerId,
      orderId: orderId,
      productId: productId,
      trackingCode: trackingCode,
      orderAmount: orderAmount,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      status: CommissionStatus.pending,
      createdAt: DateTime.now(),
    );

    _commissions.add(commission);

    // تحديث عداد التحويلات للرابط
    final linkIndex =
        _affiliateLinks.indexWhere((link) => link.trackingCode == trackingCode);
    if (linkIndex != -1) {
      final link = _affiliateLinks[linkIndex];
      _affiliateLinks[linkIndex] = link.copyWith(
        conversionCount: link.conversionCount + 1,
        totalEarnings: link.totalEarnings + commissionAmount,
      );
    }

    return commission;
  }

  // تحديث حالة العمولة
  Future<void> updateCommissionStatus({
    required String commissionId,
    required CommissionStatus status,
    String? paymentMethod,
    String? paymentReference,
  }) async {
    final index = _commissions.indexWhere((c) => c.id == commissionId);
    if (index != -1) {
      final commission = _commissions[index];
      _commissions[index] = commission.copyWith(
        status: status,
        approvedAt: status == CommissionStatus.approved
            ? DateTime.now()
            : commission.approvedAt,
        processingAt: status == CommissionStatus.processing
            ? DateTime.now()
            : commission.processingAt,
        earnedAt: status == CommissionStatus.earned
            ? DateTime.now()
            : commission.earnedAt,
        paidAt: status == CommissionStatus.paid
            ? DateTime.now()
            : commission.paidAt,
        paymentMethod: paymentMethod,
        paymentReference: paymentReference,
      );
    }
  }

  // طلب صرف العمولات
  Future<bool> requestPayout(String affiliateId) async {
    final stats = getAffiliateStats(affiliateId);
    final pendingEarnings = stats['pendingEarnings'] as double;

    if (pendingEarnings < _settings.commissionSettings.minimumPayoutAmount) {
      Get.snackbar(
        'لا يمكن الصرف',
        'الحد الأدنى للصرف هو \$${_settings.commissionSettings.minimumPayoutAmount}',
        snackPosition: SnackPosition.TOP,
      );
      return false;
    }

    // محاكاة طلب الصرف
    await Future.delayed(const Duration(seconds: 2));

    Get.snackbar(
      'تم إرسال الطلب',
      'تم إرسال طلب صرف العمولات بنجاح',
      snackPosition: SnackPosition.TOP,
    );

    return true;
  }

  // وظائف مساعدة
  String _generateTrackingCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(
        8, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  String _buildOriginalUrl(
      LinkType type, String? productId, String? categoryId, String? customUrl) {
    const baseUrl = 'https://eyewearstore.com';

    switch (type) {
      case LinkType.product:
        return '$baseUrl/product/$productId';
      case LinkType.category:
        return '$baseUrl/category/$categoryId';
      case LinkType.general:
        return customUrl ?? baseUrl;
    }
  }

  String _generateShortUrl(String trackingCode) {
    return 'https://eyew.ar/$trackingCode';
  }

  // إنشاء روابط تجريبية
  List<AffiliateLink> _generateDemoLinks() {
    return [
      AffiliateLink(
        id: 'link_001',
        affiliateId: 'aff_001',
        affiliateCode: 'AM001',
        type: LinkType.product,
        productId: '1',
        originalUrl: 'https://eyewearstore.com/product/1',
        shortUrl: 'https://eyew.ar/ABC123',
        trackingCode: 'ABC123',
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        clickCount: 45,
        conversionCount: 3,
        totalEarnings: 89.97,
      ),
      AffiliateLink(
        id: 'link_002',
        affiliateId: 'aff_001',
        affiliateCode: 'AM001',
        type: LinkType.general,
        originalUrl: 'https://eyewearstore.com',
        shortUrl: 'https://eyew.ar/DEF456',
        trackingCode: 'DEF456',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        clickCount: 23,
        conversionCount: 1,
        totalEarnings: 19.99,
      ),
    ];
  }

  // إنشاء نقرات تجريبية
  List<ClickTracking> _generateDemoClicks() {
    return [
      ClickTracking(
        id: 'click_001',
        linkId: 'link_001',
        trackingCode: 'ABC123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
        clickedAt: DateTime.now().subtract(const Duration(hours: 2)),
        converted: true,
        orderId: 'order_001',
        commissionAmount: 29.99,
      ),
    ];
  }

  // الحصول على المنتجات المتاحة للتسويق
  List<Product> getAvailableProducts() {
    // في التطبيق الحقيقي، سيتم جلب هذه البيانات من قاعدة البيانات
    return Product.demoProducts;
  }
}
